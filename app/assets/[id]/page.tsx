"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  ChevronDown,
  FileText,
  MapPin,
  Calendar,
  User,
  Wrench,
  ClipboardList,
  History,
  Edit,
  Download,
  Eye,
} from "lucide-react"

export default function AssetDetail({ params }: { params: { id: string } }) {
  const [detailsExpanded, setDetailsExpanded] = useState(true)
  const [maintenanceExpanded, setMaintenanceExpanded] = useState(false)
  const [historyExpanded, setHistoryExpanded] = useState(false)

  // Mock asset data - in real app, fetch based on params.id
  const asset = {
    id: params.id,
    name: "HVAC System - Building A",
    type: "Mechanical Equipment",
    status: "Operational",
    location: "Building A, Floor 3, Room 301",
    serialNumber: "HVAC-2024-001",
    manufacturer: "TechCorp Industries",
    model: "TC-HVAC-5000",
    installDate: "January 15, 2023",
    lastMaintenance: "February 10, 2024",
    nextMaintenance: "May 10, 2024",
    warranty: "Active until Jan 2026",
    assignedTo: "Mike Johnson",
    project: "Office Complex Renovation",
  }

  const maintenanceRecords = [
    {
      id: 1,
      type: "Preventive Maintenance",
      date: "Feb 10, 2024",
      technician: "Mike Johnson",
      status: "Completed",
      description: "Routine filter replacement and system inspection",
      nextDue: "May 10, 2024",
    },
    {
      id: 2,
      type: "Repair",
      date: "Jan 22, 2024",
      technician: "Sarah Wilson",
      status: "Completed",
      description: "Fixed temperature sensor malfunction",
      nextDue: "-",
    },
    {
      id: 3,
      type: "Inspection",
      date: "Dec 15, 2023",
      technician: "Mike Johnson",
      status: "Completed",
      description: "Annual safety inspection and certification",
      nextDue: "Dec 15, 2024",
    },
  ]

  const inspectionForms = [
    {
      id: 1,
      name: "Monthly Safety Inspection",
      type: "Safety Checklist",
      lastCompleted: "Feb 15, 2024",
      completedBy: "Mike Johnson",
      status: "Passed",
      nextDue: "Mar 15, 2024",
    },
    {
      id: 2,
      name: "Energy Efficiency Assessment",
      type: "Performance Form",
      lastCompleted: "Jan 30, 2024",
      completedBy: "Sarah Wilson",
      status: "Needs Attention",
      nextDue: "Apr 30, 2024",
    },
    {
      id: 3,
      name: "Filter Replacement Log",
      type: "Maintenance Form",
      lastCompleted: "Feb 10, 2024",
      completedBy: "Mike Johnson",
      status: "Completed",
      nextDue: "May 10, 2024",
    },
  ]

  const usageHistory = [
    { date: "Feb 2024", hours: "720", efficiency: "92%", issues: "None" },
    { date: "Jan 2024", hours: "744", efficiency: "89%", issues: "Sensor malfunction" },
    { date: "Dec 2023", hours: "720", efficiency: "94%", issues: "None" },
    { date: "Nov 2023", hours: "720", efficiency: "91%", issues: "None" },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Operational":
      case "Completed":
      case "Passed":
        return "bg-green-100 text-green-800"
      case "Needs Attention":
        return "bg-yellow-100 text-yellow-800"
      case "Out of Service":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Asset Header */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{asset.name}</h1>
          <div className="flex items-center gap-4 mt-2">
            <Badge className={getStatusColor(asset.status)}>{asset.status}</Badge>
            <Badge variant="outline">{asset.type}</Badge>
            <span className="text-gray-600">Serial: {asset.serialNumber}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit Asset
          </Button>
        </div>
      </div>

      {/* Asset Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MapPin className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Location</p>
                <p className="text-sm font-semibold">{asset.location}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Next Maintenance</p>
                <p className="text-sm font-semibold">{asset.nextMaintenance}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <User className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Assigned To</p>
                <p className="text-sm font-semibold">{asset.assignedTo}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Wrench className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Warranty</p>
                <p className="text-sm font-semibold">{asset.warranty}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Collapsible Sections */}
      <div className="space-y-4">
        {/* Asset Details */}
        <Collapsible open={detailsExpanded} onOpenChange={setDetailsExpanded}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Asset Details
                  </div>
                  <ChevronDown className={`w-5 h-5 transition-transform ${detailsExpanded ? "rotate-180" : ""}`} />
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">Basic Information</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Manufacturer:</span>
                        <span className="font-medium">{asset.manufacturer}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Model:</span>
                        <span className="font-medium">{asset.model}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Install Date:</span>
                        <span className="font-medium">{asset.installDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Project:</span>
                        <span className="font-medium">{asset.project}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">Maintenance Info</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Maintenance:</span>
                        <span className="font-medium">{asset.lastMaintenance}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Next Maintenance:</span>
                        <span className="font-medium">{asset.nextMaintenance}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Assigned Technician:</span>
                        <div className="flex items-center gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarImage src="/placeholder.svg?height=24&width=24" />
                            <AvatarFallback className="text-xs">MJ</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{asset.assignedTo}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>

        {/* Maintenance Records */}
        <Collapsible open={maintenanceExpanded} onOpenChange={setMaintenanceExpanded}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Wrench className="w-5 h-5" />
                    Maintenance Records & Forms
                  </div>
                  <ChevronDown className={`w-5 h-5 transition-transform ${maintenanceExpanded ? "rotate-180" : ""}`} />
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="pt-0 space-y-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Recent Maintenance</h4>
                  <div className="space-y-3">
                    {maintenanceRecords.map((record) => (
                      <div key={record.id} className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h5 className="font-medium text-gray-900">{record.type}</h5>
                            <p className="text-sm text-gray-600">{record.description}</p>
                          </div>
                          <Badge className={getStatusColor(record.status)}>{record.status}</Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>
                            By {record.technician} • {record.date}
                          </span>
                          {record.nextDue !== "-" && <span>Next due: {record.nextDue}</span>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Inspection Forms & Checklists</h4>
                  <div className="space-y-3">
                    {inspectionForms.map((form) => (
                      <div
                        key={form.id}
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <ClipboardList className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900">{form.name}</h5>
                            <p className="text-sm text-gray-600">
                              {form.type} • Last completed {form.lastCompleted} by {form.completedBy}
                            </p>
                            <p className="text-xs text-gray-500">Next due: {form.nextDue}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge className={getStatusColor(form.status)}>{form.status}</Badge>
                          <Button variant="ghost" size="sm">
                            <Eye className="w-4 h-4 mr-2" />
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>

        {/* Usage History */}
        <Collapsible open={historyExpanded} onOpenChange={setHistoryExpanded}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <History className="w-5 h-5" />
                    Usage History
                  </div>
                  <ChevronDown className={`w-5 h-5 transition-transform ${historyExpanded ? "rotate-180" : ""}`} />
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="pt-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Period</TableHead>
                      <TableHead>Operating Hours</TableHead>
                      <TableHead>Efficiency</TableHead>
                      <TableHead>Issues</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {usageHistory.map((record, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{record.date}</TableCell>
                        <TableCell>{record.hours}</TableCell>
                        <TableCell>{record.efficiency}</TableCell>
                        <TableCell>
                          {record.issues === "None" ? (
                            <Badge className="bg-green-100 text-green-800">None</Badge>
                          ) : (
                            <span className="text-sm text-gray-600">{record.issues}</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      </div>
    </div>
  )
}
