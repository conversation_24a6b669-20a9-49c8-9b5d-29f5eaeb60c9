"use client"

import { Textarea } from "@/components/ui/textarea"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Save, Trash2, X } from "lucide-react"

interface WorkflowFormSheetProps {
  isOpen: boolean
  onClose: () => void
  workflowData?: any // Optional, for editing existing workflow steps
  onSave: (data: any) => void
  onDelete?: (id: number) => void
}

export function WorkflowFormSheet({ isOpen, onClose, workflowData, onSave, onDelete }: WorkflowFormSheetProps) {
  const isEditMode = !!workflowData
  const [formData, setFormData] = React.useState<any>(
    workflowData || {
      name: "",
      description: "",
      statuses: [],
    },
  )

  React.useEffect(() => {
    setFormData(
      workflowData || {
        name: "",
        description: "",
        statuses: [],
      },
    )
  }, [workflowData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev: any) => ({ ...prev, [id]: value }))
  }

  const handleAddStatus = () => {
    setFormData((prev: any) => ({
      ...prev,
      statuses: [...prev.statuses, ""],
    }))
  }

  const handleStatusChange = (index: number, value: string) => {
    const updatedStatuses = formData.statuses.map((status: string, i: number) => (i === index ? value : status))
    setFormData((prev: any) => ({ ...prev, statuses: updatedStatuses }))
  }

  const handleRemoveStatus = (index: number) => {
    const updatedStatuses = formData.statuses.filter((_: string, i: number) => i !== index)
    setFormData((prev: any) => ({ ...prev, statuses: updatedStatuses }))
  }

  const handleSubmit = () => {
    onSave(formData)
    onClose()
  }

  const handleDelete = () => {
    if (workflowData?.id && onDelete) {
      onDelete(workflowData.id)
      onClose()
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[500px] lg:w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{isEditMode ? "Edit Workflow Step" : "Add New Workflow Step"}</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6 pb-20">
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Step Name</Label>
              <Input id="name" value={formData.name} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" value={formData.description} onChange={handleChange} />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Associated Statuses</h3>
            {formData.statuses.map((status: string, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  value={status}
                  onChange={(e) => handleStatusChange(index, e.target.value)}
                  placeholder="e.g., In Progress"
                />
                <Button variant="ghost" size="icon" onClick={() => handleRemoveStatus(index)}>
                  <X className="w-4 h-4 text-red-500" />
                </Button>
              </div>
            ))}
            <Button variant="outline" className="w-full bg-transparent" onClick={handleAddStatus}>
              <Plus className="w-4 h-4 mr-2" />
              Add Status
            </Button>
          </div>
        </div>
        <SheetFooter className="flex flex-col sm:flex-row gap-2 mt-4">
          {isEditMode && (
            <Button variant="destructive" onClick={handleDelete} className="w-full sm:w-auto">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Step
            </Button>
          )}
          <Button onClick={handleSubmit} className="w-full sm:w-auto">
            <Save className="w-4 h-4 mr-2" />
            {isEditMode ? "Save Changes" : "Add Step"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
