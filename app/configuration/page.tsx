"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Workflow, Users, LayoutTemplate, Group } from "lucide-react" // Import Group icon for Teams
import WorkflowABM from "./workflow/page"
import LesseeABM from "./lessee/page"
import BlueprintABM from "./blueprint/page"
import TeamABM from "./team/page" // Import the new TeamABM

export default function ConfigurationPage() {
  const [activeTab, setActiveTab] = useState("workflow")

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Configuration</h1>
        <p className="text-gray-600 mt-2">Manage core data and settings for your aviation inspection platform.</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full space-y-6">
        <TabsList className="grid w-full max-w-2xl grid-cols-4">
          {" "}
          {/* Adjusted grid-cols */}
          <TabsTrigger value="workflow">
            <Workflow className="w-4 h-4 mr-2" />
            Workflow
          </TabsTrigger>
          <TabsTrigger value="lessee">
            <Users className="w-4 h-4 mr-2" />
            Lessees
          </TabsTrigger>
          <TabsTrigger value="blueprint">
            <LayoutTemplate className="w-4 h-4 mr-2" />
            Blueprints
          </TabsTrigger>
          <TabsTrigger value="team">
            {" "}
            {/* New Team Tab */}
            <Group className="w-4 h-4 mr-2" />
            Teams
          </TabsTrigger>
        </TabsList>

        <TabsContent value="workflow">
          <WorkflowABM />
        </TabsContent>
        <TabsContent value="lessee">
          <LesseeABM />
        </TabsContent>
        <TabsContent value="blueprint">
          <BlueprintABM />
        </TabsContent>
        <TabsContent value="team">
          {" "}
          {/* New Team Content */}
          <TeamABM />
        </TabsContent>
      </Tabs>
    </div>
  )
}
