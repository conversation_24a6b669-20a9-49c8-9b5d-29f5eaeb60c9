"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Eye, Edit, MoreHorizontal, ArrowUpDown, Plane, MapPin } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { ProjectFormSheet } from "@/components/project-form-sheet" // Import the new component

export default function ProjectsPage() {
  const [filterStatus, setFilterStatus] = useState("all")
  const [isProjectSheetOpen, setIsProjectSheetOpen] = useState(false)
  const [projectToEdit, setProjectToEdit] = useState<any | null>(null)

  const [inspectionProjects, setInspectionProjects] = useState([
    {
      id: "1",
      name: "Delta Air Lines Fleet Inspection",
      lessee: "Delta Air Lines",
      status: "In Progress",
      inspectionType: "A-Check",
      progress: 75,
      inspector: "Sarah Johnson",
      assetType: "Aircraft",
      aircraft: [
        { type: "Boeing 737-800", msn: "34567", registration: "N123DL" },
        { type: "Boeing 737-900", msn: "34568", registration: "N124DL" },
      ],
      location: "Miami International (MIA)",
      startDate: "2024-01-15",
      endDate: "2024-03-30",
      estimatedCost: "$125,000",
      priority: "High",
      compliance: "FAA Part 145",
      description: "Comprehensive A-Check inspection for Delta Air Lines Boeing 737-800 fleet.",
      manager: "Sarah Johnson",
    },
    {
      id: "2",
      name: "United Airlines Heavy Maintenance",
      lessee: "United Airlines",
      status: "Scheduled",
      inspectionType: "C-Check",
      progress: 15,
      inspector: "Mike Chen",
      assetType: "Aircraft",
      aircraft: [{ type: "Boeing 777-300ER", msn: "45678", registration: "N789UA" }],
      location: "John F. Kennedy (JFK)",
      startDate: "2024-02-01",
      endDate: "2024-06-15",
      estimatedCost: "$450,000",
      priority: "High",
      compliance: "FAA Part 145",
      description: "Heavy maintenance C-Check for United Airlines Boeing 777-300ER.",
      manager: "Mike Chen",
    },
    {
      id: "3",
      name: "American Airlines Line Maintenance",
      lessee: "American Airlines",
      status: "Completed",
      inspectionType: "Line Check",
      progress: 100,
      inspector: "Emma Davis",
      assetType: "Aircraft",
      aircraft: [
        { type: "Airbus A320-200", msn: "56789", registration: "N456AA" },
        { type: "Airbus A321-200", msn: "56790", registration: "N457AA" },
        { type: "Airbus A319-100", msn: "56791", registration: "N458AA" },
      ],
      location: "Los Angeles International (LAX)",
      startDate: "2023-12-01",
      endDate: "2024-01-30",
      estimatedCost: "$85,000",
      priority: "Medium",
      compliance: "FAA Part 145",
      description: "Routine line maintenance for American Airlines Airbus fleet.",
      manager: "Emma Davis",
    },
    {
      id: "4",
      name: "Southwest Airlines Avionics Check",
      lessee: "Southwest Airlines",
      status: "In Progress",
      inspectionType: "Avionics Inspection",
      progress: 60,
      inspector: "Alex Rodriguez",
      assetType: "Aircraft",
      aircraft: [{ type: "Boeing 737-700", msn: "67890", registration: "N789SW" }],
      location: "Dallas/Fort Worth (DFW)",
      startDate: "2024-01-20",
      endDate: "2024-04-10",
      estimatedCost: "$95,000",
      priority: "Medium",
      compliance: "FAA Part 145",
      description: "Avionics system inspection for Southwest Airlines Boeing 737-700.",
      manager: "Alex Rodriguez",
    },
    {
      id: "5",
      name: "JetBlue Airways Annual Inspection",
      lessee: "JetBlue Airways",
      status: "Under Review",
      inspectionType: "Annual Inspection",
      progress: 90,
      inspector: "Lisa Wang",
      assetType: "Aircraft",
      aircraft: [
        { type: "Embraer E190", msn: "78901", registration: "N890JB" },
        { type: "Embraer E190", msn: "78902", registration: "N891JB" },
      ],
      location: "Chicago O'Hare (ORD)",
      startDate: "2024-03-01",
      endDate: "2024-04-30",
      estimatedCost: "$65,000",
      priority: "High",
      compliance: "FAA Part 145",
      description: "Annual inspection for JetBlue Airways Embraer E190 fleet.",
      manager: "Lisa Wang",
    },
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Scheduled":
        return "bg-yellow-100 text-yellow-800"
      case "Under Review":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getInspectionTypeColor = (type: string) => {
    switch (type) {
      case "A-Check":
        return "bg-blue-100 text-blue-800"
      case "C-Check":
        return "bg-purple-100 text-purple-800"
      case "Line Check":
        return "bg-green-100 text-green-800"
      case "Avionics Inspection":
        return "bg-orange-100 text-orange-800"
      case "Annual Inspection":
        return "bg-indigo-100 text-indigo-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleNewProjectClick = () => {
    setProjectToEdit(null) // Clear any previous edit data
    setIsProjectSheetOpen(true)
  }

  const handleEditProjectClick = (project: any) => {
    setProjectToEdit(project)
    setIsProjectSheetOpen(true)
  }

  const handleSaveProject = (data: any) => {
    if (data.id) {
      // Edit existing project
      setInspectionProjects((prev) => prev.map((p) => (p.id === data.id ? { ...p, ...data } : p)))
      console.log("Project updated:", data)
    } else {
      // Create new project
      const newId = (Math.max(...inspectionProjects.map((p) => Number.parseInt(p.id))) + 1).toString()
      const newProject = { ...data, id: newId, progress: 0 } // Assign a new ID and default progress
      setInspectionProjects((prev) => [...prev, newProject])
      console.log("New project created:", newProject)
    }
  }

  const handleDeleteProject = (id: string) => {
    setInspectionProjects((prev) => prev.filter((p) => p.id !== id))
    console.log("Project deleted:", id)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Inspection Projects</h1>
          <p className="text-gray-600 mt-2">Manage and track all aviation inspection projects</p>
        </div>
        <Button className="flex items-center gap-2" onClick={handleNewProjectClick}>
          <Plus className="w-4 h-4" />
          New Inspection Project
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input placeholder="Search inspection projects..." className="pl-10" />
            </div>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="under-review">Under Review</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px]">
                  <Button variant="ghost" className="h-auto p-0 font-semibold">
                    Project Name
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Lessee</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Asset</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Inspector</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {inspectionProjects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell className="font-medium">
                    <Link href={`/projects/${project.id}`} className="hover:text-blue-600">
                      {project.name}
                    </Link>
                  </TableCell>
                  <TableCell>{project.lessee}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getInspectionTypeColor(project.inspectionType)} variant="outline">
                      {project.inspectionType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Plane className="w-4 h-4 text-gray-400" />
                      <span>{project.aircraft.length}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={project.progress} className="w-16" />
                      <span className="text-sm text-gray-600">{project.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src="/placeholder.svg?height=24&width=24" />
                        <AvatarFallback className="text-xs">
                          {project.inspector
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{project.inspector}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3 text-gray-400" />
                      <span className="text-sm">{project.location}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/projects/${project.id}`}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditProjectClick(project)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Project
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <ProjectFormSheet
        isOpen={isProjectSheetOpen}
        onClose={() => setIsProjectSheetOpen(false)}
        projectData={projectToEdit}
        onSave={handleSaveProject}
        onDelete={handleDeleteProject}
      />
    </div>
  )
}
