"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Edit, Trash2, MoreH<PERSON><PERSON><PERSON> } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Image from "next/image"
import { BlueprintFormSheet } from "@/components/blueprint-form-sheet" // Import the new sheet component

export default function BlueprintABM() {
  const [blueprints, setBlueprints] = useState([
    {
      id: 1,
      name: "Boeing 737-800 Blueprint v1",
      type: "Aircraft",
      imageUrl: "/placeholder.svg?height=100&width=150",
      description: "Standard blueprint for Boeing 737-800 series.",
    },
    {
      id: 2,
      name: "Airbus A320 Engine Layout",
      type: "Engine",
      imageUrl: "/placeholder.svg?height=100&width=150",
      description: "Detailed layout of CFM56 engine for A320.",
    },
    {
      id: 3,
      name: "Boeing 777 Landing Gear Schematic",
      type: "Landing Gear",
      imageUrl: "/placeholder.svg?height=100&width=150",
      description: "Schematic for 777 main landing gear assembly.",
    },
  ])

  const [isBlueprintSheetOpen, setIsBlueprintSheetOpen] = useState(false)
  const [blueprintToEdit, setBlueprintToEdit] = useState<any | null>(null)

  const handleNewBlueprintClick = () => {
    setBlueprintToEdit(null)
    setIsBlueprintSheetOpen(true)
  }

  const handleEditBlueprintClick = (blueprint: any) => {
    setBlueprintToEdit(blueprint)
    setIsBlueprintSheetOpen(true)
  }

  const handleSaveBlueprint = (data: any) => {
    if (data.id) {
      // Edit existing blueprint
      setBlueprints((prev) => prev.map((bp) => (bp.id === data.id ? { ...bp, ...data } : bp)))
      console.log("Blueprint updated:", data)
    } else {
      // Create new blueprint
      const newId = Math.max(...blueprints.map((bp) => bp.id)) + 1
      const newBlueprint = { ...data, id: newId }
      setBlueprints((prev) => [...prev, newBlueprint])
      console.log("New blueprint created:", newBlueprint)
    }
  }

  const handleDeleteBlueprint = (id: number) => {
    setBlueprints(blueprints.filter((bp) => bp.id !== id))
    console.log("Blueprint deleted:", id)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle>Blueprint Management</CardTitle>
        <Button onClick={handleNewBlueprintClick}>
          <Plus className="w-4 h-4 mr-2" /> Upload New Blueprint
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Available Blueprints</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">ID</TableHead>
                <TableHead>Preview</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {blueprints.map((bp) => (
                <TableRow key={bp.id}>
                  <TableCell>{bp.id}</TableCell>
                  <TableCell>
                    <Image
                      src={bp.imageUrl || "/placeholder.svg"}
                      alt={bp.name}
                      width={80}
                      height={50}
                      className="rounded-md object-cover"
                    />
                  </TableCell>
                  <TableCell className="font-medium">{bp.name}</TableCell>
                  <TableCell>{bp.type}</TableCell>
                  <TableCell className="text-sm text-gray-600">{bp.description}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditBlueprintClick(bp)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteBlueprint(bp.id)}>
                          <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <BlueprintFormSheet
        isOpen={isBlueprintSheetOpen}
        onClose={() => setIsBlueprintSheetOpen(false)}
        blueprintData={blueprintToEdit}
        onSave={handleSaveBlueprint}
        onDelete={handleDeleteBlueprint}
      />
    </Card>
  )
}
