"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Edit, Trash2, MoreH<PERSON>zon<PERSON> } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { WorkflowFormSheet } from "@/components/workflow-form-sheet" // Import the new sheet component
import { Badge } from "@/components/ui/badge"

export default function WorkflowABM() {
  const [workflowSteps, setWorkflowSteps] = useState([
    {
      id: 1,
      name: "Inspection Initiated",
      description: "Project officially started, team assigned.",
      statuses: ["Active", "Pending"],
    },
    {
      id: 2,
      name: "Data Collection",
      description: "Inspectors gather findings, photos, and measurements.",
      statuses: ["In Progress", "Completed"],
    },
    {
      id: 3,
      name: "Initial Review",
      description: "Lead inspector reviews collected data for completeness.",
      statuses: ["Under Review", "Approved"],
    },
    {
      id: 4,
      name: "Defect Identification",
      description: "Open items are logged and categorized.",
      statuses: ["Open", "Closed"],
    },
    {
      id: 5,
      name: "Repair/Rectification",
      description: "Maintenance actions are performed on identified defects.",
      statuses: ["In Progress", "Completed"],
    },
    {
      id: 6,
      name: "Final Inspection",
      description: "Completed repairs are verified.",
      statuses: ["Pending", "Verified"],
    },
    {
      id: 7,
      name: "Documentation & Sign-off",
      description: "All forms and reports are finalized and signed.",
      statuses: ["Draft", "Finalized"],
    },
    {
      id: 8,
      name: "Project Closed",
      description: "Inspection project is officially completed.",
      statuses: ["Archived"],
    },
  ])

  const [isWorkflowSheetOpen, setIsWorkflowSheetOpen] = useState(false)
  const [workflowToEdit, setWorkflowToEdit] = useState<any | null>(null)

  const handleNewWorkflowClick = () => {
    setWorkflowToEdit(null)
    setIsWorkflowSheetOpen(true)
  }

  const handleEditWorkflowClick = (workflow: any) => {
    setWorkflowToEdit(workflow)
    setIsWorkflowSheetOpen(true)
  }

  const handleSaveWorkflow = (data: any) => {
    if (data.id) {
      // Edit existing workflow step
      setWorkflowSteps((prev) => prev.map((step) => (step.id === data.id ? { ...step, ...data } : step)))
      console.log("Workflow step updated:", data)
    } else {
      // Create new workflow step
      const newId = Math.max(...workflowSteps.map((step) => step.id)) + 1
      const newWorkflow = { ...data, id: newId }
      setWorkflowSteps((prev) => [...prev, newWorkflow])
      console.log("New workflow step created:", newWorkflow)
    }
  }

  const handleDeleteWorkflow = (id: number) => {
    setWorkflowSteps(workflowSteps.filter((step) => step.id !== id))
    console.log("Workflow step deleted:", id)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle>Workflow Management</CardTitle>
        <Button onClick={handleNewWorkflowClick}>
          <Plus className="w-4 h-4 mr-2" /> Add New Step
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Current Workflow Steps</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">ID</TableHead>
                <TableHead>Step Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Statuses</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {workflowSteps.map((step) => (
                <TableRow key={step.id}>
                  <TableCell>{step.id}</TableCell>
                  <TableCell className="font-medium">{step.name}</TableCell>
                  <TableCell>{step.description}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {step.statuses.map((status, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {status}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditWorkflowClick(step)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteWorkflow(step.id)}>
                          <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <WorkflowFormSheet
        isOpen={isWorkflowSheetOpen}
        onClose={() => setIsWorkflowSheetOpen(false)}
        workflowData={workflowToEdit}
        onSave={handleSaveWorkflow}
        onDelete={handleDeleteWorkflow}
      />
    </Card>
  )
}
