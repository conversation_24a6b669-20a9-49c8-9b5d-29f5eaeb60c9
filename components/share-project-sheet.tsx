"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Co<PERSON>, Link, Users, Calendar, Plus, Minus, UserPlus } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface ShareProjectSheetProps {
  isOpen: boolean
  onClose: () => void
  projectName: string
  projectId: string
}

export function ShareProjectSheet({ isOpen, onClose, projectName, projectId }: ShareProjectSheetProps) {
  const shareLink = `${window.location.origin}/projects/${projectId}`
  const [durationDays, setDurationDays] = React.useState(7) // Default 7 days
  const [sharedUsers, setSharedUsers] = React.useState([
    {
      id: 1,
      name: "<PERSON>",
      role: "Project Manager",
      avatar: "/placeholder.svg?height=32&width=32",
      permission: "Can edit",
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Lead Developer",
      avatar: "/placeholder.svg?height=32&width=32",
      permission: "Can view",
    },
  ])

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareLink)
    // Optionally, show a toast notification
    console.log("Link copied to clipboard!")
  }

  const handleAddDays = (days: number) => {
    setDurationDays((prev) => Math.max(1, prev + days)) // Ensure duration doesn't go below 1
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[500px] lg:w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Share Project: {projectName}</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6 pb-20">
          {/* Shareable Link */}
          <div className="space-y-2">
            <Label htmlFor="share-link" className="flex items-center gap-2">
              <Link className="w-4 h-4" /> Shareable Link
            </Label>
            <div className="flex gap-2">
              <Input id="share-link" value={shareLink} readOnly className="flex-1" />
              <Button variant="outline" size="icon" onClick={handleCopyLink}>
                <Copy className="w-4 h-4" />
                <span className="sr-only">Copy link</span>
              </Button>
            </div>
          </div>

          {/* People & Permissions */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Users className="w-5 h-5" /> People & Permissions
            </h3>
            <div className="space-y-3">
              {sharedUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.avatar || "/placeholder.svg"} />
                      <AvatarFallback>
                        {user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-gray-900">{user.name}</p>
                      <p className="text-sm text-gray-600">{user.role}</p>
                    </div>
                  </div>
                  <Badge variant="secondary">{user.permission}</Badge>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full bg-transparent">
              <UserPlus className="w-4 h-4 mr-2" /> Add People
            </Button>
          </div>

          {/* Duration of Visibility */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Calendar className="w-5 h-5" /> Duration of Visibility
            </h3>
            <div className="flex items-center gap-4">
              <Label htmlFor="duration-days" className="text-base">
                Link active for:
              </Label>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={() => handleAddDays(-1)}>
                  <Minus className="w-4 h-4" />
                </Button>
                <Input
                  id="duration-days"
                  type="number"
                  value={durationDays}
                  onChange={(e) => setDurationDays(Number(e.target.value))}
                  className="w-20 text-center"
                  min={1}
                />
                <span>days</span>
                <Button variant="outline" size="icon" onClick={() => handleAddDays(1)}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <p className="text-sm text-gray-500">The link will automatically expire after {durationDays} days.</p>
          </div>
        </div>
        <SheetFooter className="mt-4">
          <Button onClick={onClose} className="w-full">
            Done
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
