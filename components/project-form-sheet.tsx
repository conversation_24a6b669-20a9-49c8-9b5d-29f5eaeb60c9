"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Save, Trash2 } from "lucide-react"

interface ProjectFormSheetProps {
  isOpen: boolean
  onClose: () => void
  projectData?: any // Optional, for editing existing projects
  onSave: (data: any) => void
  onDelete?: (id: string) => void
}

export function ProjectFormSheet({ isOpen, onClose, projectData, onSave, onDelete }: ProjectFormSheetProps) {
  const isEditMode = !!projectData
  const [formData, setFormData] = React.useState<any>(
    projectData || {
      name: "",
      status: "Scheduled",
      category: "",
      progress: 0,
      description: "",
      manager: "",
      startDate: "",
      endDate: "",
      location: "",
      priority: "Medium",
      inspectionType: "",
      lessee: "",
      assetType: "Aircraft",
      aircraft: [], // For aircraft assets
    },
  )

  React.useEffect(() => {
    setFormData(
      projectData || {
        name: "",
        status: "Scheduled",
        category: "",
        progress: 0,
        description: "",
        manager: "",
        startDate: "",
        endDate: "",
        location: "",
        priority: "Medium",
        inspectionType: "",
        lessee: "",
        assetType: "Aircraft",
        aircraft: [],
      },
    )
  }, [projectData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev: any) => ({ ...prev, [id]: value }))
  }

  const handleSelectChange = (id: string, value: string) => {
    setFormData((prev: any) => ({ ...prev, [id]: value }))
  }

  const handleAddAsset = () => {
    setFormData((prev: any) => ({
      ...prev,
      aircraft: [...prev.aircraft, { model: "", msn: "", registration: "", blueprintId: "" }],
    }))
  }

  const handleAssetChange = (index: number, field: string, value: string) => {
    const updatedAircraft = formData.aircraft.map((asset: any, i: number) =>
      i === index ? { ...asset, [field]: value } : asset,
    )
    setFormData((prev: any) => ({ ...prev, aircraft: updatedAircraft }))
  }

  const handleRemoveAsset = (index: number) => {
    const updatedAircraft = formData.aircraft.filter((_: any, i: number) => i !== index)
    setFormData((prev: any) => ({ ...prev, aircraft: updatedAircraft }))
  }

  const handleSubmit = () => {
    onSave(formData)
    onClose()
  }

  const handleDelete = () => {
    if (projectData?.id && onDelete) {
      onDelete(projectData.id)
      onClose()
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[600px] lg:w-[700px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{isEditMode ? "Edit Project" : "Create New Project"}</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6 pb-20">
          {/* Project Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Project Details</h3>
            <div>
              <Label htmlFor="name">Project Name</Label>
              <Input id="name" value={formData.name} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" value={formData.description} onChange={handleChange} />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(val) => handleSelectChange("status", val)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Scheduled">Scheduled</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Under Review">Under Review</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(val) => handleSelectChange("priority", val)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input id="startDate" type="date" value={formData.startDate} onChange={handleChange} />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input id="endDate" type="date" value={formData.endDate} onChange={handleChange} />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="manager">Project Manager</Label>
                <Input id="manager" value={formData.manager} onChange={handleChange} />
              </div>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input id="location" value={formData.location} onChange={handleChange} />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="lessee">Lessee</Label>
                <Input id="lessee" value={formData.lessee} onChange={handleChange} />
              </div>
              <div>
                <Label htmlFor="inspectionType">Inspection Type</Label>
                <Input id="inspectionType" value={formData.inspectionType} onChange={handleChange} />
              </div>
            </div>
          </div>

          {/* Asset Management */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Assets</h3>
            {formData.aircraft.map((asset: any, index: number) => (
              <div key={index} className="border p-4 rounded-lg space-y-3 relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 text-red-500 hover:bg-red-50"
                  onClick={() => handleRemoveAsset(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`asset-model-${index}`}>Model</Label>
                    <Input
                      id={`asset-model-${index}`}
                      value={asset.model}
                      onChange={(e) => handleAssetChange(index, "model", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`asset-msn-${index}`}>MSN</Label>
                    <Input
                      id={`asset-msn-${index}`}
                      value={asset.msn}
                      onChange={(e) => handleAssetChange(index, "msn", e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor={`asset-registration-${index}`}>Registration</Label>
                  <Input
                    id={`asset-registration-${index}`}
                    value={asset.registration}
                    onChange={(e) => handleAssetChange(index, "registration", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor={`asset-blueprint-${index}`}>Blueprint (ID/Name)</Label>
                  <Input
                    id={`asset-blueprint-${index}`}
                    value={asset.blueprintId}
                    placeholder="e.g., Boeing737-Blueprint-v2"
                    onChange={(e) => handleAssetChange(index, "blueprintId", e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This would link to a blueprint managed in the Configuration section.
                  </p>
                </div>
              </div>
            ))}
            <Button variant="outline" className="w-full bg-transparent" onClick={handleAddAsset}>
              <Plus className="w-4 h-4 mr-2" />
              Add Asset
            </Button>
          </div>
        </div>
        <SheetFooter className="flex flex-col sm:flex-row gap-2 mt-4">
          {isEditMode && (
            <Button variant="destructive" onClick={handleDelete} className="w-full sm:w-auto">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Project
            </Button>
          )}
          <Button onClick={handleSubmit} className="w-full sm:w-auto">
            <Save className="w-4 h-4 mr-2" />
            {isEditMode ? "Save Changes" : "Create Project"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
