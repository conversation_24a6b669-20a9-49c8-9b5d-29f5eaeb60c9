"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MapPin, Search, Filter, ChevronRight, Building2, Users, Calendar } from "lucide-react"

export default function MapView() {
  const [selectedProject, setSelectedProject] = useState<number | null>(null)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const projects = [
    {
      id: 1,
      name: "Downtown Office Complex",
      status: "In Progress",
      location: "New York, NY",
      coordinates: { lat: 40.7128, lng: -74.006 },
      team: 12,
      completion: 75,
      startDate: "Jan 2024",
    },
    {
      id: 2,
      name: "Residential Tower",
      status: "Planning",
      location: "Los Angeles, CA",
      coordinates: { lat: 34.0522, lng: -118.2437 },
      team: 8,
      completion: 25,
      startDate: "Mar 2024",
    },
    {
      id: 3,
      name: "Shopping Center Renovation",
      status: "Completed",
      location: "Chicago, IL",
      coordinates: { lat: 41.8781, lng: -87.6298 },
      team: 15,
      completion: 100,
      startDate: "Sep 2023",
    },
    {
      id: 4,
      name: "Industrial Warehouse",
      status: "In Progress",
      location: "Houston, TX",
      coordinates: { lat: 29.7604, lng: -95.3698 },
      team: 10,
      completion: 60,
      startDate: "Feb 2024",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Planning":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex gap-6">
      {/* Map Container */}
      <div className="flex-1 relative">
        <Card className="h-full">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Project Locations
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[calc(100%-5rem)]">
            {/* Mock Map - In a real app, you'd use Google Maps, Mapbox, etc. */}
            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-green-50 rounded-lg relative overflow-hidden">
              <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 text-lg">Interactive Map View</p>
                  <p className="text-gray-500 text-sm mt-2">Project locations would be displayed here</p>
                </div>
              </div>

              {/* Mock Map Markers */}
              {projects.map((project, index) => (
                <div
                  key={project.id}
                  className={`absolute w-8 h-8 rounded-full flex items-center justify-center cursor-pointer transition-all ${
                    selectedProject === project.id ? "bg-blue-600 scale-125 shadow-lg" : "bg-red-500 hover:scale-110"
                  }`}
                  style={{
                    top: `${20 + index * 15}%`,
                    left: `${25 + index * 20}%`,
                  }}
                  onClick={() => setSelectedProject(project.id)}
                >
                  <MapPin className="w-4 h-4 text-white" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project List Sidebar */}
      <div className={`transition-all duration-300 ${sidebarCollapsed ? "w-12" : "w-96"}`}>
        <Card className="h-full">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              {!sidebarCollapsed && (
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Projects ({projects.length})
                </CardTitle>
              )}
              <Button variant="ghost" size="icon" onClick={() => setSidebarCollapsed(!sidebarCollapsed)}>
                <ChevronRight
                  className={`w-4 h-4 transition-transform ${sidebarCollapsed ? "rotate-0" : "rotate-180"}`}
                />
              </Button>
            </div>

            {!sidebarCollapsed && (
              <div className="flex gap-2 mt-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input placeholder="Search projects..." className="pl-10" />
                </div>
                <Button variant="outline" size="icon">
                  <Filter className="w-4 h-4" />
                </Button>
              </div>
            )}
          </CardHeader>

          {!sidebarCollapsed && (
            <CardContent className="space-y-4 overflow-y-auto">
              {projects.map((project) => (
                <div
                  key={project.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedProject === project.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                  onClick={() => setSelectedProject(project.id)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium text-gray-900 text-sm">{project.name}</h3>
                    <Badge className={`text-xs ${getStatusColor(project.status)}`}>{project.status}</Badge>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <MapPin className="w-3 h-3" />
                      {project.location}
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-600">
                      <div className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        {project.team} members
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {project.startDate}
                      </div>
                    </div>

                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{project.completion}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className="bg-blue-600 h-1.5 rounded-full transition-all"
                          style={{ width: `${project.completion}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  )
}
