"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Save, Trash2, Plus, X } from "lucide-react"

interface TeamFormSheetProps {
  isOpen: boolean
  onClose: () => void
  teamData?: any // Optional, for editing existing teams
  onSave: (data: any) => void
  onDelete?: (id: number) => void
}

export function TeamFormSheet({ isOpen, onClose, teamData, onSave, onDelete }: TeamFormSheetProps) {
  const isEditMode = !!teamData
  const [formData, setFormData] = React.useState<any>(
    teamData || {
      name: "",
      location: "",
      lead: "",
      members: 0,
      activeInspections: 0,
      completionRate: 0,
      specialization: "",
      certifications: [],
      contact: { phone: "", email: "" },
    },
  )

  React.useEffect(() => {
    setFormData(
      teamData || {
        name: "",
        location: "",
        lead: "",
        members: 0,
        activeInspections: 0,
        completionRate: 0,
        specialization: "",
        certifications: [],
        contact: { phone: "", email: "" },
      },
    )
  }, [teamData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    if (id === "phone" || id === "email") {
      setFormData((prev: any) => ({
        ...prev,
        contact: { ...prev.contact, [id]: value },
      }))
    } else if (["members", "activeInspections", "completionRate"].includes(id)) {
      setFormData((prev: any) => ({ ...prev, [id]: Number(value) }))
    } else {
      setFormData((prev: any) => ({ ...prev, [id]: value }))
    }
  }

  const handleAddCertification = () => {
    setFormData((prev: any) => ({
      ...prev,
      certifications: [...prev.certifications, ""],
    }))
  }

  const handleCertificationChange = (index: number, value: string) => {
    const updatedCerts = formData.certifications.map((cert: string, i: number) => (i === index ? value : cert))
    setFormData((prev: any) => ({ ...prev, certifications: updatedCerts }))
  }

  const handleRemoveCertification = (index: number) => {
    const updatedCerts = formData.certifications.filter((_: string, i: number) => i !== index)
    setFormData((prev: any) => ({ ...prev, certifications: updatedCerts }))
  }

  const handleSubmit = () => {
    onSave(formData)
    onClose()
  }

  const handleDelete = () => {
    if (teamData?.id && onDelete) {
      onDelete(teamData.id)
      onClose()
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[600px] lg:w-[700px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{isEditMode ? "Edit Team" : "Create New Team"}</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6 pb-20">
          {/* Team Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Team Details</h3>
            <div>
              <Label htmlFor="name">Team Name</Label>
              <Input id="name" value={formData.name} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="location">Location</Label>
              <Input id="location" value={formData.location} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="lead">Team Lead</Label>
              <Input id="lead" value={formData.lead} onChange={handleChange} />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="members">Members</Label>
                <Input id="members" type="number" value={formData.members} onChange={handleChange} />
              </div>
              <div>
                <Label htmlFor="activeInspections">Active Inspections</Label>
                <Input
                  id="activeInspections"
                  type="number"
                  value={formData.activeInspections}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="specialization">Specialization</Label>
              <Input id="specialization" value={formData.specialization} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="completionRate">Completion Rate (%)</Label>
              <Input
                id="completionRate"
                type="number"
                value={formData.completionRate}
                onChange={handleChange}
                max={100}
                min={0}
              />
            </div>
          </div>

          {/* Certifications */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Certifications</h3>
            {formData.certifications.map((cert: string, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  value={cert}
                  onChange={(e) => handleCertificationChange(index, e.target.value)}
                  placeholder="e.g., FAA Part 145"
                />
                <Button variant="ghost" size="icon" onClick={() => handleRemoveCertification(index)}>
                  <X className="w-4 h-4 text-red-500" />
                </Button>
              </div>
            ))}
            <Button variant="outline" className="w-full bg-transparent" onClick={handleAddCertification}>
              <Plus className="w-4 h-4 mr-2" />
              Add Certification
            </Button>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Information</h3>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input id="phone" value={formData.contact.phone} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" value={formData.contact.email} onChange={handleChange} />
            </div>
          </div>
        </div>
        <SheetFooter className="flex flex-col sm:flex-row gap-2 mt-4">
          {isEditMode && (
            <Button variant="destructive" onClick={handleDelete} className="w-full sm:w-auto">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Team
            </Button>
          )}
          <Button onClick={handleSubmit} className="w-full sm:w-auto">
            <Save className="w-4 h-4 mr-2" />
            {isEditMode ? "Save Changes" : "Create Team"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
