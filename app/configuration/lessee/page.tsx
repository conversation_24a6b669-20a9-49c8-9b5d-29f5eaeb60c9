"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Edit, Trash2, Plane, MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { LesseeFormSheet } from "@/components/lessee-form-sheet" // Import the new sheet component

export default function LesseeABM() {
  const [lessees, setLessees] = useState([
    { id: 1, name: "Delta Air Lines", contactPerson: "<PERSON>", email: "<EMAIL>", aircraftCount: 120 },
    { id: 2, name: "United Airlines", contactPerson: "<PERSON>", email: "<EMAIL>", aircraftCount: 150 },
    { id: 3, name: "American Airlines", contactPerson: "<PERSON>", email: "<EMAIL>", aircraftCount: 180 },
    { id: 4, name: "Southwest Airlines", contactPerson: "<PERSON>", email: "<EMAIL>", aircraftCount: 90 },
  ])

  const [isLesseeSheetOpen, setIsLesseeSheetOpen] = useState(false)
  const [lesseeToEdit, setLesseeToEdit] = useState<any | null>(null)

  const handleNewLesseeClick = () => {
    setLesseeToEdit(null)
    setIsLesseeSheetOpen(true)
  }

  const handleEditLesseeClick = (lessee: any) => {
    setLesseeToEdit(lessee)
    setIsLesseeSheetOpen(true)
  }

  const handleSaveLessee = (data: any) => {
    if (data.id) {
      // Edit existing lessee
      setLessees((prev) => prev.map((lessee) => (lessee.id === data.id ? { ...lessee, ...data } : lessee)))
      console.log("Lessee updated:", data)
    } else {
      // Create new lessee
      const newId = Math.max(...lessees.map((lessee) => lessee.id)) + 1
      const newLessee = { ...data, id: newId }
      setLessees((prev) => [...prev, newLessee])
      console.log("New lessee created:", newLessee)
    }
  }

  const handleDeleteLessee = (id: number) => {
    setLessees(lessees.filter((lessee) => lessee.id !== id))
    console.log("Lessee deleted:", id)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle>Lessee Management</CardTitle>
        <Button onClick={handleNewLesseeClick}>
          <Plus className="w-4 h-4 mr-2" /> Add New Lessee
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Registered Lessees</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Contact Person</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Aircraft Count</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {lessees.map((lessee) => (
                <TableRow key={lessee.id}>
                  <TableCell>{lessee.id}</TableCell>
                  <TableCell className="font-medium">{lessee.name}</TableCell>
                  <TableCell>{lessee.contactPerson}</TableCell>
                  <TableCell>{lessee.email}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Plane className="w-4 h-4 text-gray-500" />
                      {lessee.aircraftCount}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditLesseeClick(lessee)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteLessee(lessee.id)}>
                          <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <LesseeFormSheet
        isOpen={isLesseeSheetOpen}
        onClose={() => setIsLesseeSheetOpen(false)}
        lesseeData={lesseeToEdit}
        onSave={handleSaveLessee}
        onDelete={handleDeleteLessee}
      />
    </Card>
  )
}
