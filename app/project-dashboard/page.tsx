import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, Users, Target, TrendingUp, Clock, CheckCircle2, AlertCircle, FileText } from "lucide-react"

export default function ProjectDashboard() {
  const projectInfo = {
    name: "Website Redesign Project",
    status: "In Progress",
    progress: 75,
    startDate: "Jan 15, 2024",
    endDate: "Mar 30, 2024",
    manager: "<PERSON>",
    budget: "$45,000",
    spent: "$33,750",
  }

  const teamMembers = [
    { name: "<PERSON>", role: "Project Manager", avatar: "/placeholder.svg?height=40&width=40" },
    { name: "<PERSON>", role: "Lead Developer", avatar: "/placeholder.svg?height=40&width=40" },
    { name: "<PERSON>", role: "UI/UX Designer", avatar: "/placeholder.svg?height=40&width=40" },
    { name: "<PERSON>", role: "Frontend Developer", avatar: "/placeholder.svg?height=40&width=40" },
  ]

  const milestones = [
    { name: "Project Kickoff", date: "Jan 15", status: "completed" },
    { name: "Design Phase", date: "Feb 1", status: "completed" },
    { name: "Development Phase", date: "Feb 15", status: "in-progress" },
    { name: "Testing & QA", date: "Mar 15", status: "pending" },
    { name: "Launch", date: "Mar 30", status: "pending" },
  ]

  const recentActivity = [
    { action: "Design mockups approved", user: "Emma Davis", time: "2 hours ago" },
    { action: "Homepage development completed", user: "Mike Chen", time: "5 hours ago" },
    { action: "Client feedback incorporated", user: "Sarah Johnson", time: "1 day ago" },
    { action: "Navigation component updated", user: "Alex Rodriguez", time: "2 days ago" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{projectInfo.name}</h1>
          <div className="flex items-center gap-4 mt-2">
            <Badge className="bg-blue-100 text-blue-800">{projectInfo.status}</Badge>
            <span className="text-gray-600">Managed by {projectInfo.manager}</span>
          </div>
        </div>
      </div>

      {/* Project Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Progress</p>
                <p className="text-2xl font-bold">{projectInfo.progress}%</p>
              </div>
            </div>
            <Progress value={projectInfo.progress} className="mt-4" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Calendar className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Timeline</p>
                <p className="text-lg font-semibold">
                  {projectInfo.startDate} - {projectInfo.endDate}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Team Size</p>
                <p className="text-2xl font-bold">{teamMembers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <TrendingUp className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Budget Used</p>
                <p className="text-lg font-semibold">
                  {projectInfo.spent} / {projectInfo.budget}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Team Members */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Team Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {teamMembers.map((member, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Avatar>
                    <AvatarImage src={member.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {member.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-medium text-gray-900">{member.name}</h4>
                    <p className="text-sm text-gray-600">{member.role}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Project Milestones */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Milestones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-center gap-3">
                  {milestone.status === "completed" && <CheckCircle2 className="w-5 h-5 text-green-600" />}
                  {milestone.status === "in-progress" && <Clock className="w-5 h-5 text-blue-600" />}
                  {milestone.status === "pending" && <AlertCircle className="w-5 h-5 text-gray-400" />}
                  <div className="flex-1">
                    <h4
                      className={`font-medium ${
                        milestone.status === "completed"
                          ? "text-green-900"
                          : milestone.status === "in-progress"
                            ? "text-blue-900"
                            : "text-gray-600"
                      }`}
                    >
                      {milestone.name}
                    </h4>
                    <p className="text-sm text-gray-500">{milestone.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-gray-900">{activity.action}</p>
                  <p className="text-sm text-gray-600">
                    by {activity.user} • {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
