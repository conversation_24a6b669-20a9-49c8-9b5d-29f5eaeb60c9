"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, Activity, CheckCircle2, Users, Calendar, FolderOpen, Plane, MapPin } from "lucide-react"

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  // Mock data for aviation inspection project status widgets
  const projectStats = [
    {
      title: "Total Inspections",
      value: "24",
      subtitle: "Active: 18",
      icon: FolderOpen,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      trend: "+12%",
    },
    {
      title: "Completed",
      value: "6",
      subtitle: "This month: 3",
      icon: CheckCircle2,
      color: "text-green-600",
      bgColor: "bg-green-100",
      trend: "+25%",
    },
    {
      title: "In Progress",
      value: "12",
      subtitle: "On schedule: 10",
      icon: Activity,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      trend: "+8%",
    },
    {
      title: "Overdue",
      value: "2",
      subtitle: "Critical: 1",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
      trend: "-15%",
    },
  ]

  const inspectionsByStatus = [
    { status: "Scheduled", count: 4, color: "bg-yellow-500" },
    { status: "In Progress", count: 12, color: "bg-blue-500" },
    { status: "Under Review", count: 6, color: "bg-purple-500" },
    { status: "Completed", count: 6, color: "bg-green-500" },
  ]

  const recentInspections = [
    { name: "A-Check Boeing 737", status: "In Progress", progress: 75, assets: 2, dueDate: "Mar 30", location: "MIA" },
    { name: "C-Check Airbus A320", status: "Scheduled", progress: 25, assets: 1, dueDate: "Jun 15", location: "JFK" },
    {
      name: "Line Maintenance A330",
      status: "Completed",
      progress: 100,
      assets: 3,
      dueDate: "Jan 30",
      location: "LAX",
    },
    {
      name: "Heavy Maintenance 777",
      status: "In Progress",
      progress: 60,
      assets: 1,
      dueDate: "Apr 10",
      location: "DFW",
    },
    {
      name: "Annual Inspection EMB190",
      status: "Under Review",
      progress: 90,
      assets: 2,
      dueDate: "Mar 15",
      location: "ORD",
    },
  ]

  const teamPerformance = [
    { name: "Inspection Team A", inspections: 8, completion: 85, location: "Miami" },
    { name: "Inspection Team B", inspections: 4, completion: 92, location: "New York" },
    { name: "Quality Assurance", inspections: 6, completion: 78, location: "Los Angeles" },
    { name: "Compliance Team", inspections: 3, completion: 95, location: "Dallas" },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Scheduled":
        return "bg-yellow-100 text-yellow-800"
      case "Under Review":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="w-full space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Project Dashboard</h1>
        <p className="text-gray-600 mt-2">Monitor aviation inspection progress and compliance status</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full space-y-6">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="map">Map</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="w-full space-y-6">
          {/* Aviation Inspection Status Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {projectStats.map((stat, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-full ${stat.bgColor}`}>
                      <stat.icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                    <div className="text-right">
                      <span
                        className={`text-sm font-medium ${stat.trend.startsWith("+") ? "text-green-600" : "text-red-600"}`}
                      >
                        {stat.trend}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</p>
                    <p className="text-sm font-medium text-gray-900 mb-1">{stat.title}</p>
                    <p className="text-xs text-gray-500">{stat.subtitle}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Content Grid - Full Width */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Inspection Status Distribution */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Inspection Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inspectionsByStatus.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded ${item.color}`}></div>
                        <span className="text-sm font-medium">{item.status}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">{item.count} inspections</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${item.color}`}
                            style={{ width: `${(item.count / 24) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Fleet Compliance Overview */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Fleet Compliance Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-4">
                    <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">87%</p>
                        <p className="text-xs text-gray-600">Compliant</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <p className="text-lg font-bold text-blue-600">142</p>
                    <p className="text-xs text-gray-600">Aircraft</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-green-600">124</p>
                    <p className="text-xs text-gray-600">Compliant</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Inspections and Team Performance - Full Width */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Inspections */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plane className="w-5 h-5" />
                  Recent Inspections
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentInspections.map((inspection, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{inspection.name}</h4>
                          <Badge className={getStatusColor(inspection.status)}>{inspection.status}</Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                          <span className="flex items-center gap-1">
                            <Plane className="w-3 h-3" />
                            {inspection.assets} aircraft
                          </span>
                          <span className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {inspection.location}
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {inspection.dueDate}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={inspection.progress} className="flex-1 h-2" />
                          <span className="text-xs text-gray-500">{inspection.progress}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Team Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Inspection Team Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {teamPerformance.map((team, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{team.name}</h4>
                          <p className="text-xs text-gray-600">{team.location}</p>
                        </div>
                        <span className="text-sm text-gray-600">{team.inspections} inspections</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={team.completion} className="flex-1 h-2" />
                        <span className="text-xs text-gray-500">{team.completion}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Inspection Schedule Timeline - Full Width */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Inspection Schedule Timeline</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  This Month
                </Button>
                <Button variant="ghost" size="sm">
                  Next Month
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Inspection Schedule Timeline</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Gantt chart showing inspection schedules and maintenance windows
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold">Inspection Project Overview</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((project) => (
              <Card key={project} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Lessee</p>
                    <h3 className="font-semibold text-gray-900">Delta Air Lines</h3>
                    <p className="text-sm text-gray-600">A-Check Inspection</p>
                  </div>
                  <div className="flex justify-center gap-2 mt-3">
                    <span className="px-2 py-1 bg-gray-100 rounded text-xs">MSN: 12345</span>
                    <span className="px-2 py-1 bg-blue-100 rounded text-xs">MSN: 67890</span>
                    <span className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                      <Plane className="w-3 h-3" />
                    </span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold">Jan 15</p>
                      <p className="text-xs text-gray-600">Start Date</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">Mar 30</p>
                      <p className="text-xs text-gray-600">End Date</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">45</p>
                      <p className="text-xs text-gray-600">Days</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-center">
                    <div>
                      <p className="text-xl font-bold text-gray-900">100</p>
                      <p className="text-xs text-gray-600">Total Items</p>
                    </div>
                    <div>
                      <p className="text-xl font-bold text-red-500">55</p>
                      <p className="text-xs text-gray-600">Open</p>
                    </div>
                    <div>
                      <p className="text-xl font-bold text-blue-500">40</p>
                      <p className="text-xs text-gray-600">In Progress</p>
                    </div>
                    <div>
                      <p className="text-xl font-bold text-green-500">5</p>
                      <p className="text-xs text-gray-600">Completed</p>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <p className="text-xs text-gray-600 text-center mb-3">Items without updates beyond 10 days</p>
                    <div className="flex justify-center gap-8">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-1">
                          <span className="text-lg font-bold text-purple-600">22</span>
                        </div>
                        <p className="text-xs text-gray-600">by acceptance</p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-1">
                          <span className="text-lg font-bold text-purple-600">13</span>
                        </div>
                        <p className="text-xs text-gray-600">by provision</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="map" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold">Inspection Locations</h2>
          </div>

          <div className="h-[700px] flex gap-6">
            {/* Inspection List Sidebar */}
            <div className="w-72 flex-shrink-0 space-y-4 overflow-y-auto">
              {[
                { lessee: "Delta Air Lines", type: "A-Check", aircraft: "Boeing 737", msn: "12345", location: "MIA" },
                { lessee: "United Airlines", type: "C-Check", aircraft: "Airbus A320", msn: "67890", location: "JFK" },
                {
                  lessee: "American Airlines",
                  type: "Heavy Maintenance",
                  aircraft: "Boeing 777",
                  msn: "11111",
                  location: "LAX",
                },
                {
                  lessee: "Southwest Airlines",
                  type: "Line Maintenance",
                  aircraft: "Boeing 737",
                  msn: "22222",
                  location: "DFW",
                },
                {
                  lessee: "JetBlue Airways",
                  type: "Annual Inspection",
                  aircraft: "Embraer 190",
                  msn: "33333",
                  location: "ORD",
                },
                { lessee: "Alaska Airlines", type: "A-Check", aircraft: "Boeing 737", msn: "44444", location: "SEA" },
                { lessee: "Spirit Airlines", type: "C-Check", aircraft: "Airbus A320", msn: "55555", location: "FLL" },
              ].map((inspection, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex gap-3">
                      <div className="w-16 h-12 bg-blue-100 rounded flex items-center justify-center">
                        <Plane className="w-6 h-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{inspection.lessee}</h4>
                        <p className="text-xs text-gray-600">{inspection.type}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {inspection.aircraft} - MSN: {inspection.msn}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <MapPin className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{inspection.location}</span>
                        </div>
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-xs">Total: 100</span>
                          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-purple-600">22</span>
                          </div>
                          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-purple-600">13</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Map Container */}
            <Card className="flex-1">
              <CardContent className="p-0 h-full">
                <div className="w-full h-full bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 rounded-lg relative overflow-hidden">
                  {/* Mock World Map Background */}
                  <div className="absolute inset-0 opacity-30">
                    <div className="w-full h-full bg-gradient-to-br from-green-800 to-blue-900"></div>
                  </div>

                  {/* Mock Inspection Location Markers */}
                  {[
                    { top: "40%", left: "25%", name: "Miami (MIA) - Delta A-Check" },
                    { top: "35%", left: "28%", name: "New York (JFK) - United C-Check" },
                    { top: "45%", left: "20%", name: "Los Angeles (LAX) - American Heavy" },
                    { top: "42%", left: "22%", name: "Dallas (DFW) - Southwest Line" },
                    { top: "38%", left: "26%", name: "Chicago (ORD) - JetBlue Annual" },
                    { top: "32%", left: "18%", name: "Seattle (SEA) - Alaska A-Check" },
                    { top: "43%", left: "24%", name: "Fort Lauderdale (FLL) - Spirit C-Check" },
                  ].map((marker, index) => (
                    <div
                      key={index}
                      className="absolute w-4 h-4 bg-yellow-500 rounded-full cursor-pointer hover:scale-125 transition-transform border-2 border-white"
                      style={{ top: marker.top, left: marker.left }}
                      title={marker.name}
                    >
                      <div className="absolute -top-1 -left-1 w-6 h-6 bg-yellow-500 rounded-full opacity-50 animate-ping"></div>
                      <Plane className="w-2 h-2 text-white absolute top-1 left-1" />
                    </div>
                  ))}

                  {/* Map Controls */}
                  <div className="absolute top-4 right-4 flex flex-col gap-2">
                    <Button size="sm" variant="secondary" className="w-8 h-8 p-0">
                      +
                    </Button>
                    <Button size="sm" variant="secondary" className="w-8 h-8 p-0">
                      -
                    </Button>
                  </div>

                  {/* Map Legend */}
                  <div className="absolute bottom-4 left-4 bg-white/90 rounded-lg p-3">
                    <h4 className="text-xs font-semibold mb-2">Inspection Types</h4>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span className="text-xs">Active Inspections</span>
                      </div>
                    </div>
                  </div>

                  {/* Map Attribution */}
                  <div className="absolute bottom-2 right-2 text-xs text-white opacity-75">© Aviation Map Data</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
