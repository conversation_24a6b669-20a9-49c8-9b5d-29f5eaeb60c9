"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Save, Trash2 } from "lucide-react"

interface LesseeFormSheetProps {
  isOpen: boolean
  onClose: () => void
  lesseeData?: any // Optional, for editing existing lessees
  onSave: (data: any) => void
  onDelete?: (id: number) => void
}

export function LesseeFormSheet({ isOpen, onClose, lesseeData, onSave, onDelete }: LesseeFormSheetProps) {
  const isEditMode = !!lesseeData
  const [formData, setFormData] = React.useState<any>(
    lesseeData || {
      name: "",
      contactPerson: "",
      email: "",
      aircraftCount: 0,
    },
  )

  React.useEffect(() => {
    setFormData(
      lesseeD<PERSON> || {
        name: "",
        contactPerson: "",
        email: "",
        aircraftCount: 0,
      },
    )
  }, [lesseeData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData((prev: any) => ({ ...prev, [id]: id === "aircraftCount" ? Number(value) : value }))
  }

  const handleSubmit = () => {
    onSave(formData)
    onClose()
  }

  const handleDelete = () => {
    if (lesseeData?.id && onDelete) {
      onDelete(lesseeData.id)
      onClose()
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[500px] lg:w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{isEditMode ? "Edit Lessee" : "Add New Lessee"}</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6 pb-20">
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Lessee Name</Label>
              <Input id="name" value={formData.name} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="contactPerson">Contact Person</Label>
              <Input id="contactPerson" value={formData.contactPerson} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" value={formData.email} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="aircraftCount">Aircraft Count</Label>
              <Input id="aircraftCount" type="number" value={formData.aircraftCount} onChange={handleChange} />
            </div>
          </div>
        </div>
        <SheetFooter className="flex flex-col sm:flex-row gap-2 mt-4">
          {isEditMode && (
            <Button variant="destructive" onClick={handleDelete} className="w-full sm:w-auto">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Lessee
            </Button>
          )}
          <Button onClick={handleSubmit} className="w-full sm:w-auto">
            <Save className="w-4 h-4 mr-2" />
            {isEditMode ? "Save Changes" : "Add Lessee"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
