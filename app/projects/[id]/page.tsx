"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet" // Import SheetFooter
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Calendar,
  Users,
  Target,
  FileText,
  Folder,
  Edit,
  Share,
  Plane,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Cog,
  Camera,
  Upload,
  ChevronDown,
  ClipboardList,
  MessageSquare,
  Save,
} from "lucide-react"
import Image from "next/image"
import { AssetTimeline } from "@/components/asset-timeline"
import { ShareProjectSheet } from "@/components/share-project-sheet" // Import the new share sheet

export default function ProjectDetail({ params }: { params: { id: string } }) {
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedAsset, setSelectedAsset] = useState<number | null>(null)
  const [assetTab, setAssetTab] = useState("details")
  const [selectedHotspot, setSelectedHotspot] = useState<number | null>(null)

  // State for each form's collapsible state - now initialized to false (collapsed)
  const [cCheckFormExpanded, setCCheckFormExpanded] = useState(false)
  const [structuralFormExpanded, setStructuralFormExpanded] = useState(false)
  const [engineFormExpanded, setEngineFormExpanded] = useState(false)
  const [adFormExpanded, setAdFormExpanded] = useState(false)

  // State for the selected open item to display in the side panel
  const [selectedOpenItem, setSelectedOpenItem] = useState<any | null>(null)
  // State for editable open item data in the side panel
  const [editableOpenItem, setEditableOpenItem] = useState<any | null>(null)

  // State for share project sheet
  const [isShareSheetOpen, setIsShareSheetOpen] = useState(false)

  // Mock project data - single asset type (Aircraft)
  const project = {
    id: params.id,
    name: "Delta Air Lines Boeing 737-800 C-Check",
    status: "In Progress",
    category: "C-Check Inspection",
    progress: 65,
    description:
      "Comprehensive C-Check inspection for Delta Air Lines Boeing 737-800 fleet. This inspection includes structural assessments, engine borescope inspections, landing gear overhauls, avionics testing, and compliance verification with all applicable Airworthiness Directives (ADs).",
    manager: "Captain Sarah Mitchell",
    startDate: "February 1, 2024",
    endDate: "April 15, 2024",
    location: "Miami International Airport (MIA)",
    priority: "High",
    inspectionType: "C-Check",
    lessee: "Delta Air Lines",
    assetType: "Aircraft", // Single asset type
    team: [
      { name: "Captain Sarah Mitchell", role: "Chief Inspector", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Mike Rodriguez", role: "Structural Inspector", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Emma Thompson", role: "Avionics Specialist", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "James Wilson", role: "Engine Inspector", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Lisa Chen", role: "Documentation Specialist", avatar: "/placeholder.svg?height=40&width=40" },
    ],
  }

  // Main assets (Aircraft only)
  const aircraft = [
    {
      id: 1,
      registration: "N801DL",
      model: "Boeing 737-800",
      msn: "30854",
      status: "In Inspection",
      progress: 75,
      location: "Hangar 3",
      nextDue: "C-Check: 2,500 FH remaining",
      tsn: "45,230",
      csn: "8,945",
      // Sub-components (not main project assets)
      engines: [
        { position: "Left", model: "CFM56-7B24", msn: "872456", tsn: "22,100", csn: "15,800" },
        { position: "Right", model: "CFM56-7B24", msn: "872457", tsn: "21,850", csn: "15,650" },
      ],
      landingGear: [
        { position: "Nose", msn: "NLG-445789", cycles: "8,945" },
        { position: "Main Left", msn: "MLG-445790", cycles: "8,945" },
        { position: "Main Right", msn: "MLG-445791", cycles: "8,945" },
      ],
      apu: { model: "GTCP131-9A", msn: "P-890234", tsn: "8,450", csn: "12,300" },
      // Asset-specific open items
      openItems: [
        {
          id: 1,
          itemNumber: "C-001",
          description: "Minor corrosion found on cargo bay frame 15",
          category: "Structural",
          priority: "Medium",
          status: "Open",
          assignedTo: "Mike Rodriguez",
          dueDate: "Mar 25, 2024",
          findings: "Surface corrosion on aluminum frame, requires cleaning and treatment",
          comments: [
            {
              id: 1,
              author: "Mike Rodriguez",
              date: "Mar 20, 2024",
              text: "Initial finding, requires further assessment.",
            },
            {
              id: 2,
              author: "Sarah Mitchell",
              date: "Mar 21, 2024",
              text: "Agreed, schedule NDT for detailed inspection.",
            },
          ],
          attachments: [
            { id: 1, name: "Corrosion_Photo_1.jpg", url: "/placeholder.svg?height=100&width=100", type: "image" },
            { id: 2, name: "Corrosion_Report.pdf", url: "/placeholder.svg?height=100&width=100", type: "pdf" },
          ],
        },
        {
          id: 2,
          itemNumber: "C-002",
          description: "Left engine oil filter requires replacement",
          category: "Engine",
          priority: "High",
          status: "In Progress",
          assignedTo: "James Wilson",
          dueDate: "Mar 20, 2024",
          findings: "Metal particles found in oil filter during inspection",
          comments: [],
          attachments: [],
        },
        {
          id: 3,
          itemNumber: "C-003",
          description: "Cabin door seal replacement required",
          category: "Interior",
          priority: "Low",
          status: "Completed",
          assignedTo: "Emma Thompson",
          dueDate: "Mar 15, 2024",
          findings: "Door seal showing wear, replaced per maintenance manual",
          comments: [],
          attachments: [],
        },
      ],
    },
    {
      id: 2,
      registration: "N802DL",
      model: "Boeing 737-800",
      msn: "30855",
      status: "Completed",
      progress: 100,
      location: "Line Maintenance",
      nextDue: "A-Check: 450 FH remaining",
      tsn: "42,100",
      csn: "8,200",
      engines: [
        { position: "Left", model: "CFM56-7B24", msn: "872458", tsn: "20,300", csn: "14,900" },
        { position: "Right", model: "CFM56-7B24", msn: "872459", tsn: "20,150", csn: "14,850" },
      ],
      landingGear: [
        { position: "Nose", msn: "NLG-445792", cycles: "8,200" },
        { position: "Main Left", msn: "MLG-445793", cycles: "8,200" },
        { position: "Main Right", msn: "MLG-445794", cycles: "8,200" },
      ],
      apu: { model: "GTCP131-9A", msn: "P-890235", tsn: "7,850", csn: "11,800" },
      openItems: [
        {
          id: 4,
          itemNumber: "C-004",
          description: "All inspection items completed successfully",
          category: "General",
          priority: "Info",
          status: "Completed",
          assignedTo: "Captain Sarah Mitchell",
          dueDate: "Mar 10, 2024",
          findings: "C-Check completed with no outstanding items",
          comments: [],
          attachments: [],
        },
      ],
    },
  ]

  // Blueprint hotspots for photo attachment (asset-specific)
  const getBlueprintHotspots = (assetId: number) => [
    { id: 1, x: 50, y: 15, label: "Nose Section", photos: 3 },
    { id: 2, x: 25, y: 45, label: "Left Wing", photos: 5 },
    { id: 3, x: 75, y: 45, label: "Right Wing", photos: 4 },
    { id: 4, x: 50, y: 70, label: "Fuselage Center", photos: 8 },
    { id: 5, x: 50, y: 90, label: "Tail Section", photos: 2 },
    { id: 6, x: 35, y: 55, label: "Left Engine", photos: 6 },
    { id: 7, x: 65, y: 55, label: "Right Engine", photos: 7 },
  ]

  const documents = [
    { name: "C-Check Work Package", type: "PDF", size: "4.2 MB", date: "Feb 1, 2024" },
    { name: "Structural Inspection Reports", type: "PDF", size: "3.1 MB", date: "Feb 15, 2024" },
    { name: "Engine Borescope Documentation", type: "PDF", size: "5.4 MB", date: "Feb 20, 2024" },
    { name: "Airworthiness Directives Compliance", type: "XLS", size: "2.1 MB", date: "Feb 5, 2024" },
    { name: "LLP Status Summary", type: "XLS", size: "956 KB", date: "Mar 1, 2024" },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Inspection":
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Pending":
      case "Open":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case "In Inspection":
      case "In Progress":
        return <Settings className="w-4 h-4 text-blue-600" />
      case "Pending":
      case "Open":
        return <Clock className="w-4 h-4 text-yellow-600" />
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const selectedAssetData = selectedAsset ? aircraft.find((a) => a.id === selectedAsset) : null

  // Handler for opening the side panel and setting editable data
  const handleOpenItemClick = (item: any) => {
    setSelectedOpenItem(item)
    setEditableOpenItem({ ...item }) // Create a copy for editing
  }

  // Handler for saving changes to an open item
  const handleSaveOpenItem = () => {
    console.log("Saving changes to open item:", editableOpenItem)
    // In a real application, you would send this data to your backend
    // and then update the local state (e.g., `aircraft` array) with the new data.
    // For this mock, we'll just close the panel.
    setSelectedOpenItem(null)
    setEditableOpenItem(null)
  }

  // Handler for input changes in the side panel
  const handleOpenItemInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setEditableOpenItem((prev: any) => ({
      ...prev,
      [id]: value,
    }))
  }

  const handleOpenItemSelectChange = (id: string, value: string) => {
    setEditableOpenItem((prev: any) => ({
      ...prev,
      [id]: value,
    }))
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Project Header */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
          <div className="flex items-center gap-4 mt-2">
            <Badge className="bg-blue-100 text-blue-800">{project.status}</Badge>
            <Badge variant="outline">{project.category}</Badge>
            <Badge variant="outline" className="bg-purple-50 text-purple-700">
              <Plane className="w-3 h-3 mr-1" />
              {project.lessee}
            </Badge>
            <span className="text-gray-600">Managed by {project.manager}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsShareSheetOpen(true)}>
            {" "}
            {/* Share button */}
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit Project
          </Button>
        </div>
      </div>

      {/* Project Overview Cards */}
      {/* Tabbed Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="assets">Assets</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Target className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Progress</p>
                    <p className="text-2xl font-bold">{project.progress}%</p>
                  </div>
                </div>
                <Progress value={project.progress} className="mt-4" />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Calendar className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Timeline</p>
                    <p className="text-sm font-semibold">{project.startDate}</p>
                    <p className="text-sm font-semibold">{project.endDate}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Users className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Team Size</p>
                    <p className="text-2xl font-bold">{project.team.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Plane className="w-5 h-5 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Assets</p>
                    <p className="text-2xl font-bold">{aircraft.length}</p>
                    <p className="text-xs text-gray-500">{project.assetType}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Project Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Description</h4>
                  <p className="text-gray-600 mb-6">{project.description}</p>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Start Date:</span>
                      <span className="font-medium">{project.startDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">End Date:</span>
                      <span className="font-medium">{project.endDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span className="font-medium">{project.location}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Inspection Type:</span>
                      <Badge className="bg-blue-100 text-blue-800">{project.inspectionType}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Priority:</span>
                      <Badge className="bg-red-100 text-red-800">{project.priority}</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Inspection Team</h4>
                  <div className="space-y-3">
                    {project.team.map((member, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <Avatar>
                          <AvatarImage src={member.avatar || "/placeholder.svg"} />
                          <AvatarFallback>
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h5 className="font-medium text-gray-900">{member.name}</h5>
                          <p className="text-sm text-gray-600">{member.role}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Assets Tab */}
        <TabsContent value="assets" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plane className="w-5 h-5" />
                {project.assetType} Assets ({aircraft.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {aircraft.map((asset) => (
                  <Collapsible
                    key={asset.id}
                    open={selectedAsset === asset.id}
                    onOpenChange={(open) => setSelectedAsset(open ? asset.id : null)}
                  >
                    <div className="border rounded-lg bg-gray-50">
                      <CollapsibleTrigger asChild>
                        <div className="p-6 cursor-pointer hover:bg-gray-100 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Plane className="w-5 h-5 text-blue-600" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{asset.registration}</h3>
                                <p className="text-sm text-gray-600">
                                  {asset.model} • <span className="font-mono font-semibold">MSN: {asset.msn}</span>
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-right">
                                <div className="flex items-center gap-2 mb-1">
                                  {getStatusIcon(asset.status)}
                                  <Badge className={getStatusColor(asset.status)}>{asset.status}</Badge>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Progress value={asset.progress} className="w-16" />
                                  <span className="text-sm font-medium">{asset.progress}%</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="bg-red-50 text-red-700">
                                  {asset.openItems.filter((item) => item.status !== "Completed").length} Open Items
                                </Badge>
                                <ChevronDown
                                  className={`w-5 h-5 transition-transform ${selectedAsset === asset.id ? "rotate-180" : ""}`}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </CollapsibleTrigger>

                      <CollapsibleContent>
                        <div className="border-t bg-white">
                          {/* Asset-specific tabs */}
                          <Tabs value={assetTab} onValueChange={setAssetTab} className="p-6">
                            <TabsList className="grid w-full max-w-2xl grid-cols-5">
                              <TabsTrigger value="details">Details</TabsTrigger>
                              <TabsTrigger value="forms">Forms</TabsTrigger>
                              <TabsTrigger value="blueprint">Blueprint</TabsTrigger>
                              <TabsTrigger value="openitems">Open Items</TabsTrigger>
                              <TabsTrigger value="timeline">Timeline</TabsTrigger>
                            </TabsList>

                            {/* Asset Details */}
                            <TabsContent value="details" className="mt-6">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                  <h4 className="font-medium text-gray-900 mb-2">Aircraft Details</h4>
                                  <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Location:</span>
                                      <span>{asset.location}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">TSN:</span>
                                      <span>{asset.tsn} hours</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">CSN:</span>
                                      <span>{asset.csn} cycles</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Next Due:</span>
                                      <span>{asset.nextDue}</span>
                                    </div>
                                  </div>
                                </div>

                                <div>
                                  <h4 className="font-medium text-gray-900 mb-2">Engines</h4>
                                  <div className="space-y-2">
                                    {asset.engines.map((engine, idx) => (
                                      <div key={idx} className="p-2 bg-gray-50 rounded border">
                                        <div className="flex items-center gap-2 mb-1">
                                          <Cog className="w-3 h-3 text-orange-600" />
                                          <span className="text-sm font-medium">{engine.position}</span>
                                        </div>
                                        <p className="text-xs text-gray-600">
                                          {engine.model} • MSN: {engine.msn}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                          TSN: {engine.tsn} • CSN: {engine.csn}
                                        </p>
                                      </div>
                                    ))}
                                  </div>
                                </div>

                                <div>
                                  <h4 className="font-medium text-gray-900 mb-2">Landing Gear & APU</h4>
                                  <div className="space-y-2">
                                    {asset.landingGear.map((gear, idx) => (
                                      <div key={idx} className="p-2 bg-gray-50 rounded border">
                                        <div className="flex items-center gap-2 mb-1">
                                          <Settings className="w-3 h-3 text-green-600" />
                                          <span className="text-sm font-medium">{gear.position}</span>
                                        </div>
                                        <p className="text-xs text-gray-600">MSN: {gear.msn}</p>
                                        <p className="text-xs text-gray-500">Cycles: {gear.cycles}</p>
                                      </div>
                                    ))}
                                    <div className="p-2 bg-gray-50 rounded border">
                                      <div className="flex items-center gap-2 mb-1">
                                        <Settings className="w-3 h-3 text-purple-600" />
                                        <span className="text-sm font-medium">APU</span>
                                      </div>
                                      <p className="text-xs text-gray-600">
                                        {asset.apu.model} • MSN: {asset.apu.msn}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        TSN: {asset.apu.tsn} • CSN: {asset.apu.csn}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TabsContent>

                            {/* Asset-specific Forms */}
                            <TabsContent value="forms" className="mt-6 space-y-6">
                              <Collapsible open={cCheckFormExpanded} onOpenChange={setCCheckFormExpanded}>
                                <Card>
                                  <CollapsibleTrigger asChild>
                                    <CardHeader className="cursor-pointer hover:bg-gray-50">
                                      <CardTitle className="text-lg flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Plane className="w-5 h-5" />
                                          C-Check Inspection Form - {asset.registration}
                                        </div>
                                        <ChevronDown
                                          className={`w-5 h-5 transition-transform ${cCheckFormExpanded ? "rotate-180" : ""}`}
                                        />
                                      </CardTitle>
                                    </CardHeader>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent>
                                    <AircraftCCheckForm asset={asset} />
                                  </CollapsibleContent>
                                </Card>
                              </Collapsible>

                              <Collapsible open={structuralFormExpanded} onOpenChange={setStructuralFormExpanded}>
                                <Card>
                                  <CollapsibleTrigger asChild>
                                    <CardHeader className="cursor-pointer hover:bg-gray-50">
                                      <CardTitle className="text-lg flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <FileText className="w-5 h-5" />
                                          Structural Inspection Checklist - {asset.registration}
                                        </div>
                                        <ChevronDown
                                          className={`w-5 h-5 transition-transform ${structuralFormExpanded ? "rotate-180" : ""}`}
                                        />
                                      </CardTitle>
                                    </CardHeader>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent>
                                    <StructuralInspectionForm asset={asset} />
                                  </CollapsibleContent>
                                </Card>
                              </Collapsible>

                              <Collapsible open={engineFormExpanded} onOpenChange={setEngineFormExpanded}>
                                <Card>
                                  <CollapsibleTrigger asChild>
                                    <CardHeader className="cursor-pointer hover:bg-gray-50">
                                      <CardTitle className="text-lg flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Cog className="w-5 h-5" />
                                          Engine Inspection Form - {asset.registration}
                                        </div>
                                        <ChevronDown
                                          className={`w-5 h-5 transition-transform ${engineFormExpanded ? "rotate-180" : ""}`}
                                        />
                                      </CardTitle>
                                    </CardHeader>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent>
                                    <EngineInspectionForm asset={asset} />
                                  </CollapsibleContent>
                                </Card>
                              </Collapsible>

                              <Collapsible open={adFormExpanded} onOpenChange={setAdFormExpanded}>
                                <Card>
                                  <CollapsibleTrigger asChild>
                                    <CardHeader className="cursor-pointer hover:bg-gray-50">
                                      <CardTitle className="text-lg flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <ClipboardList className="w-5 h-5" />
                                          Airworthiness Directive Compliance - {asset.registration}
                                        </div>
                                        <ChevronDown
                                          className={`w-5 h-5 transition-transform ${adFormExpanded ? "rotate-180" : ""}`}
                                        />
                                      </CardTitle>
                                    </CardHeader>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent>
                                    <ADComplianceForm asset={asset} />
                                  </CollapsibleContent>
                                </Card>
                              </Collapsible>
                            </TabsContent>

                            {/* Asset-specific Blueprint */}
                            <TabsContent value="blueprint" className="mt-6">
                              <div className="relative bg-gradient-to-br from-blue-900 to-blue-800 rounded-lg p-8">
                                <div className="relative mx-auto max-w-4xl">
                                  <Image
                                    src="/images/plane-blueprint.png"
                                    alt={`${asset.registration} Blueprint`}
                                    width={800}
                                    height={600}
                                    className="w-full h-auto"
                                  />

                                  {/* Interactive Hotspots */}
                                  {getBlueprintHotspots(asset.id).map((hotspot) => (
                                    <div key={hotspot.id}>
                                      <button
                                        className="absolute w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white font-bold text-sm transition-all hover:scale-110 shadow-lg"
                                        style={{
                                          left: `${hotspot.x}%`,
                                          top: `${hotspot.y}%`,
                                          transform: "translate(-50%, -50%)",
                                        }}
                                        onClick={() => setSelectedHotspot(hotspot.id)}
                                      >
                                        {hotspot.photos}
                                      </button>
                                    </div>
                                  ))}
                                </div>

                                <div className="mt-6 bg-white/10 rounded-lg p-4">
                                  <h4 className="text-white font-medium mb-2">
                                    Blueprint Legend - {asset.registration}
                                  </h4>
                                  <div className="flex flex-wrap gap-4 text-sm text-white/80">
                                    <div className="flex items-center gap-2">
                                      <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold">
                                        #
                                      </div>
                                      <span>Inspection Points (Click to add photos)</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-4 h-4 bg-blue-400 rounded-full"></div>
                                      <span>Structural Components</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TabsContent>

                            {/* Asset-specific Open Items */}
                            <TabsContent value="openitems" className="mt-6">
                              <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                  <h4 className="font-semibold text-gray-900">Open Items - {asset.registration}</h4>
                                  <Button size="sm">
                                    <ClipboardList className="w-4 h-4 mr-2" />
                                    Add Item
                                  </Button>
                                </div>

                                <Card>
                                  <CardContent className="p-0">
                                    <Table>
                                      <TableHeader>
                                        <TableRow>
                                          <TableHead>Item #</TableHead>
                                          <TableHead>Description</TableHead>
                                          <TableHead>Category</TableHead>
                                          <TableHead>Priority</TableHead>
                                          <TableHead>Status</TableHead>
                                          <TableHead>Assigned To</TableHead>
                                          <TableHead>Due Date</TableHead>
                                          {/* Removed Actions TableHead */}
                                        </TableRow>
                                      </TableHeader>
                                      <TableBody>
                                        {asset.openItems.map((item: any) => (
                                          <TableRow
                                            key={item.id}
                                            onClick={() => handleOpenItemClick(item)}
                                            className="cursor-pointer hover:bg-gray-50"
                                          >
                                            <TableCell className="font-mono font-medium">{item.itemNumber}</TableCell>
                                            <TableCell>
                                              <div>
                                                <p className="font-medium">{item.description}</p>
                                                <p className="text-sm text-gray-600">{item.findings}</p>
                                              </div>
                                            </TableCell>
                                            <TableCell>
                                              <Badge variant="outline">{item.category}</Badge>
                                            </TableCell>
                                            <TableCell>
                                              <Badge className={getPriorityColor(item.priority)}>{item.priority}</Badge>
                                            </TableCell>
                                            <TableCell>
                                              <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                                            </TableCell>
                                            <TableCell>
                                              <div className="flex items-center gap-2">
                                                <Avatar className="w-6 h-6">
                                                  <AvatarImage src="/placeholder.svg?height=24&width=24" />
                                                  <AvatarFallback className="text-xs">
                                                    {item.assignedTo
                                                      .split(" ")
                                                      .map((n: string) => n[0])
                                                      .join("")}
                                                  </AvatarFallback>
                                                </Avatar>
                                                <span className="text-sm">{item.assignedTo}</span>
                                              </div>
                                            </TableCell>
                                            <TableCell>{item.dueDate}</TableCell>
                                            {/* Removed Actions TableCell */}
                                          </TableRow>
                                        ))}
                                      </TableBody>
                                    </Table>
                                  </CardContent>
                                </Card>
                              </div>
                            </TabsContent>
                            <TabsContent value="timeline" className="mt-6">
                              <AssetTimeline asset={asset} />
                            </TabsContent>
                          </Tabs>
                        </div>
                      </CollapsibleContent>
                    </div>
                  </Collapsible>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="w-5 h-5" />
                Project Documentation & Records
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Project Documents</h4>
                  <div className="space-y-3">
                    {documents.map((doc, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                            <FileText className="w-4 h-4 text-gray-600" />
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900">{doc.name}</h5>
                            <p className="text-sm text-gray-600">
                              {doc.type} • {doc.size} • {doc.date}
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          Download
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Project Notes</h4>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-600">
                      C-Check inspection progressing according to schedule. N801DL requires additional structural
                      inspection due to minor corrosion findings in the cargo bay. Engine borescope inspections
                      completed with no significant findings. All aircraft documentation is current and compliant.
                    </p>
                    <p className="text-xs text-gray-500 mt-2">Last updated: 2 days ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Open Item Details Sheet (Right Side Panel) */}
      <Sheet open={!!selectedOpenItem} onOpenChange={() => setSelectedOpenItem(null)}>
        <SheetContent side="right" className="w-full md:w-[500px] lg:w-[600px] overflow-y-auto">
          {selectedOpenItem && editableOpenItem && (
            <>
              <SheetHeader>
                <SheetTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  Open Item: {editableOpenItem.itemNumber} - {editableOpenItem.description}
                </SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Item Details</h4>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Input
                        id="description"
                        value={editableOpenItem.description}
                        onChange={handleOpenItemInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={editableOpenItem.category}
                        onValueChange={(value) => handleOpenItemSelectChange("category", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Structural">Structural</SelectItem>
                          <SelectItem value="Engine">Engine</SelectItem>
                          <SelectItem value="Interior">Interior</SelectItem>
                          <SelectItem value="Avionics">Avionics</SelectItem>
                          <SelectItem value="General">General</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="priority">Priority</Label>
                      <Select
                        value={editableOpenItem.priority}
                        onValueChange={(value) => handleOpenItemSelectChange("priority", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="High">High</SelectItem>
                          <SelectItem value="Medium">Medium</SelectItem>
                          <SelectItem value="Low">Low</SelectItem>
                          <SelectItem value="Info">Info</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={editableOpenItem.status}
                        onValueChange={(value) => handleOpenItemSelectChange("status", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Open">Open</SelectItem>
                          <SelectItem value="In Progress">In Progress</SelectItem>
                          <SelectItem value="Completed">Completed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="assignedTo">Assigned To</Label>
                      <Input id="assignedTo" value={editableOpenItem.assignedTo} onChange={handleOpenItemInputChange} />
                    </div>
                    <div>
                      <Label htmlFor="dueDate">Due Date</Label>
                      <Input
                        id="dueDate"
                        type="text" // Consider using a date picker component for real apps
                        value={editableOpenItem.dueDate}
                        onChange={handleOpenItemInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="findings">Findings</Label>
                      <Textarea id="findings" value={editableOpenItem.findings} onChange={handleOpenItemInputChange} />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Comments</h4>
                  <div className="space-y-4 max-h-64 overflow-y-auto pr-2">
                    {editableOpenItem.comments && editableOpenItem.comments.length > 0 ? (
                      editableOpenItem.comments.map((comment: any) => (
                        <div key={comment.id} className="bg-gray-50 p-3 rounded-lg">
                          <div className="flex items-center gap-2 mb-1">
                            <Avatar className="w-6 h-6">
                              <AvatarImage
                                src={
                                  comment.author === "Mike Rodriguez"
                                    ? "/placeholder.svg?height=24&width=24&query=Mike Rodriguez"
                                    : comment.author === "Sarah Mitchell"
                                      ? "/placeholder.svg?height=24&width=24&query=Sarah Mitchell"
                                      : "/placeholder.svg?height=24&width=24"
                                }
                              />
                              <AvatarFallback className="text-xs">
                                {comment.author
                                  .split(" ")
                                  .map((n: string) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium text-gray-900">{comment.author}</span>
                            <span className="text-xs text-gray-500">• {comment.date}</span>
                          </div>
                          <p className="text-sm text-gray-700">{comment.text}</p>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No comments yet.</p>
                    )}
                  </div>
                  <div className="mt-4">
                    <Label htmlFor={`comment-input-${editableOpenItem.id}`}>Add a comment</Label>
                    <Textarea
                      id={`comment-input-${editableOpenItem.id}`}
                      placeholder="Type your comment here..."
                      className="mt-2"
                    />
                    <Button size="sm" className="mt-2">
                      Add Comment
                    </Button>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Attachments</h4>
                  <div className="space-y-3 max-h-64 overflow-y-auto pr-2">
                    {editableOpenItem.attachments && editableOpenItem.attachments.length > 0 ? (
                      editableOpenItem.attachments.map((attachment: any) => (
                        <div
                          key={attachment.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                              {attachment.type === "image" ? (
                                <Camera className="w-4 h-4 text-gray-600" />
                              ) : (
                                <FileText className="w-4 h-4 text-gray-600" />
                              )}
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">{attachment.name}</h5>
                              <p className="text-sm text-gray-600">{attachment.type.toUpperCase()} File</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" asChild>
                            <a href={attachment.url} target="_blank" rel="noopener noreferrer">
                              View
                            </a>
                          </Button>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No attachments yet.</p>
                    )}
                  </div>
                  <div className="mt-4">
                    <Label htmlFor={`attachment-upload-${editableOpenItem.id}`}>Upload Attachment</Label>
                    <Input id={`attachment-upload-${editableOpenItem.id}`} type="file" className="mt-2" />
                    <Button size="sm" className="mt-2">
                      <Upload className="w-4 h-4 mr-2" />
                      Upload
                    </Button>
                  </div>
                </div>
              </div>
              <SheetFooter className="mt-6">
                <Button onClick={handleSaveOpenItem} className="w-full">
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </SheetFooter>
            </>
          )}
        </SheetContent>
      </Sheet>

      {/* Share Project Sheet */}
      <ShareProjectSheet
        isOpen={isShareSheetOpen}
        onClose={() => setIsShareSheetOpen(false)}
        projectName={project.name}
        projectId={project.id}
      />
    </div>
  )
}

// Asset-specific forms (now receive asset data)
function AircraftCCheckForm({ asset }: { asset: any }) {
  return (
    <CardContent className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="aircraft-msn">Aircraft MSN</Label>
          <Input id="aircraft-msn" defaultValue={asset.msn} />
        </div>
        <div>
          <Label htmlFor="flight-hours">Current TSN (Hours)</Label>
          <Input id="flight-hours" defaultValue={asset.tsn} />
        </div>
        <div>
          <Label htmlFor="flight-cycles">Current CSN (Cycles)</Label>
          <Input id="flight-cycles" defaultValue={asset.csn} />
        </div>
      </div>

      <div>
        <Label htmlFor="c-check-type">C-Check Type</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select C-Check type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="c1">C1 Check</SelectItem>
            <SelectItem value="c2">C2 Check</SelectItem>
            <SelectItem value="c3">C3 Check</SelectItem>
            <SelectItem value="c4">C4 Check</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Required C-Check Inspections</Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="structural-c" />
            <Label htmlFor="structural-c">Structural Inspection</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="engine-borescope-c" />
            <Label htmlFor="engine-borescope-c">Engine Borescope</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="landing-gear-c" />
            <Label htmlFor="landing-gear-c">Landing Gear Inspection</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="avionics-c" />
            <Label htmlFor="avionics-c">Avionics Systems Check</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="interior-c" />
            <Label htmlFor="interior-c">Interior Inspection</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="exterior-c" />
            <Label htmlFor="exterior-c">Exterior Inspection</Label>
          </div>
        </div>
      </div>
    </CardContent>
  )
}

function StructuralInspectionForm({ asset }: { asset: any }) {
  return (
    <CardContent className="space-y-4">
      <div>
        <Label htmlFor="corrosion-findings">Corrosion Findings</Label>
        <Textarea id="corrosion-findings" placeholder="Document any corrosion findings..." />
      </div>

      <div>
        <Label htmlFor="crack-inspection">Crack Inspection Results</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select result" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="no-cracks">No Cracks Found</SelectItem>
            <SelectItem value="minor-cracks">Minor Cracks - Repairable</SelectItem>
            <SelectItem value="major-cracks">Major Cracks - Requires Replacement</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Structural Areas Inspected</Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="fuselage" />
            <Label htmlFor="fuselage">Fuselage</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="wings" />
            <Label htmlFor="wings">Wings</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="empennage" />
            <Label htmlFor="empennage">Empennage</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="cargo-bay" />
            <Label htmlFor="cargo-bay">Cargo Bay</Label>
          </div>
        </div>
      </div>
    </CardContent>
  )
}

function EngineInspectionForm({ asset }: { asset: any }) {
  return (
    <CardContent className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="engine-position">Engine Position</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select position" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="left">Left Engine</SelectItem>
              <SelectItem value="right">Right Engine</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="engine-msn-sub">Engine MSN</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select engine" />
            </SelectTrigger>
            <SelectContent>
              {asset.engines.map((engine: any, idx: number) => (
                <SelectItem key={idx} value={engine.msn}>
                  {engine.position} - {engine.msn}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="borescope-results">Borescope Inspection Results</Label>
        <Textarea id="borescope-results" placeholder="Document borescope findings..." />
      </div>

      <div className="space-y-2">
        <Label>Engine Components Inspected</Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="compressor" />
            <Label htmlFor="compressor">Compressor</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="combustor" />
            <Label htmlFor="combustor">Combustor</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="turbine" />
            <Label htmlFor="turbine">Turbine</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="accessories" />
            <Label htmlFor="accessories">Accessories</Label>
          </div>
        </div>
      </div>
    </CardContent>
  )
}

function ADComplianceForm({ asset }: { asset: any }) {
  return (
    <CardContent className="space-y-4">
      <div>
        <Label htmlFor="ad-review">AD Review Status</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="complete">Review Complete</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="pending">Pending Review</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="outstanding-ads">Outstanding ADs</Label>
        <Textarea id="outstanding-ads" placeholder="List any outstanding ADs requiring action..." />
      </div>

      <div className="space-y-2">
        <Label>AD Categories Reviewed</Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="airframe-ads" />
            <Label htmlFor="airframe-ads">Airframe ADs</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="engine-ads" />
            <Label htmlFor="engine-ads">Engine ADs</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="apu-ads" />
            <Label htmlFor="apu-ads">APU ADs</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="component-ads" />
            <Label htmlFor="component-ads">Component ADs</Label>
          </div>
        </div>
      </div>
    </CardContent>
  )
}
