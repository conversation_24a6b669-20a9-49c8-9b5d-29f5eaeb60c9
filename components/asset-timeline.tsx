import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { RefreshCw, MessageSquare, FileText, Plane, ClipboardList, <PERSON><PERSON><PERSON><PERSON>gle, User } from "lucide-react"

export function AssetTimeline({ asset }: { asset: any }) {
  // Mock timeline data for an asset
  const timelineEvents = [
    {
      id: 1,
      type: "status_update",
      icon: Plane,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-100",
      date: "Jul 25, 8:40 PM",
      title: `${asset.registration} Status Updated to 'In Inspection'`,
      description: "The aircraft has officially entered the C-Check inspection phase at Hangar 3.",
      details: null,
      user: { name: "System", avatar: null }, // System event
    },
    {
      id: 2,
      type: "form_submission",
      icon: ClipboardList,
      iconColor: "text-green-600",
      bgColor: "bg-green-100",
      date: "Jul 25, 8:15 PM",
      title: "Structural Inspection Checklist Submitted",
      description: "Initial structural inspection completed by <PERSON>.",
      details: {
        title: "Initial Structural Findings",
        content: "Minor corrosion found on cargo bay frame 15. Requires further assessment.",
      },
      user: { name: "<PERSON>", avatar: "/placeholder.svg?height=40&width=40" },
    },
    {
      id: 3,
      type: "comment",
      icon: MessageSquare,
      iconColor: "text-purple-600",
      bgColor: "bg-purple-100",
      date: "Jul 25, 6:05 PM",
      title: "New Comment on Open Item C-001",
      description: "Mike Rodriguez added a comment to 'Minor corrosion found on cargo bay frame 15'.",
      details: {
        title: "Comment Added",
        content: "Initial finding, requires further assessment.",
      },
      user: { name: "Mike Rodriguez", avatar: "/placeholder.svg?height=40&width=40" },
    },
    {
      id: 4,
      type: "attachment_upload",
      icon: FileText,
      iconColor: "text-orange-600",
      bgColor: "bg-orange-100",
      date: "Jul 25, 4:05 PM",
      title: "Photo Uploaded to Nose Section Blueprint",
      description: "New photos added to the Nose Section hotspot on the aircraft blueprint.",
      details: {
        title: "Uploaded Files",
        content: "Nose_Section_Photo_1.jpg, Nose_Section_Photo_2.jpg",
      },
      user: { name: "Emma Thompson", avatar: "/placeholder.svg?height=40&width=40" },
    },
    {
      id: 5,
      type: "open_item_status",
      icon: AlertTriangle,
      iconColor: "text-red-600",
      bgColor: "bg-red-100",
      date: "Jul 25, 2:05 PM",
      title: "Open Item C-002 Status Changed to 'In Progress'",
      description: "Left engine oil filter replacement is now in progress.",
      details: null,
      user: { name: "James Wilson", avatar: "/placeholder.svg?height=40&width=40" },
    },
    {
      id: 6,
      type: "asset_creation",
      icon: Plane,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-100",
      date: "Jul 24, 9:05 PM",
      title: `${asset.registration} Added to Project`,
      description: `Aircraft ${asset.registration} (${asset.model}, MSN: ${asset.msn}) was added to the project.`,
      details: null,
      user: { name: "Lisa Chen", avatar: "/placeholder.svg?height=40&width=40" },
    },
    {
      id: 7,
      type: "comment",
      icon: MessageSquare,
      iconColor: "text-purple-600",
      bgColor: "bg-purple-100",
      date: "Jul 24, 7:00 PM",
      title: "Discussion on Engine Borescope Findings",
      description: "Sarah Mitchell commented on the engine borescope report.",
      details: {
        title: "Comment",
        content: "Reviewing the borescope images, no immediate concerns, but monitor during next inspection.",
      },
      user: { name: "Captain Sarah Mitchell", avatar: "/placeholder.svg?height=40&width=40" },
    },
    {
      id: 8,
      type: "form_submission",
      icon: ClipboardList,
      iconColor: "text-green-600",
      bgColor: "bg-green-100",
      date: "Jul 24, 5:30 PM",
      title: "AD Compliance Form Submitted",
      description: "Airworthiness Directive compliance verified for all applicable ADs.",
      details: null,
      user: { name: "Lisa Chen", avatar: "/placeholder.svg?height=40&width=40" },
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="w-5 h-5" />
          Asset Activity Timeline - {asset.registration}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative pl-8">
          {/* Vertical line */}
          <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

          {timelineEvents.map((event) => (
            <div key={event.id} className="mb-8 flex items-start">
              {/* Icon/Avatar */}
              <div className="relative z-10 -ml-4 mr-4 flex-shrink-0">
                {event.user && event.user.avatar ? (
                  <Avatar className="w-8 h-8 border-2 border-white">
                    <AvatarImage src={event.user.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {event.user.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${event.bgColor} border-2 border-white`}
                  >
                    {event.icon ? (
                      <event.icon className={`w-4 h-4 ${event.iconColor}`} />
                    ) : (
                      <User className="w-4 h-4 text-gray-600" />
                    )}
                  </div>
                )}
              </div>

              {/* Event Content */}
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900">{event.title}</h3>
                  <span className="text-xs text-gray-500">{event.date}</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{event.description}</p>

                {event.details && (
                  <Card className="mt-3 bg-gray-50 border-gray-200 shadow-none">
                    <CardContent className="p-4">
                      <h4 className="font-semibold text-gray-800 mb-1">{event.details.title}</h4>
                      <p className="text-sm text-gray-700">{event.details.content}</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
