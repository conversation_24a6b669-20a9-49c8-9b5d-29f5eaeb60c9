"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON>, CheckCircle2, AlertTriangle, Info, XCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area" // Assuming you have ScrollArea from shadcn/ui

interface NotificationSheetProps {
  isOpen: boolean
  onClose: () => void
}

export function NotificationSheet({ isOpen, onClose }: NotificationSheetProps) {
  const notifications = [
    {
      id: 1,
      type: "info",
      icon: Info,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-50",
      title: "New Project Created",
      message: "Project 'Boeing 737 C-Check' has been initiated.",
      time: "2 min ago",
    },
    {
      id: 2,
      type: "alert",
      icon: AlertTriangle,
      iconColor: "text-orange-600",
      bgColor: "bg-orange-50",
      title: "Overdue Inspection Item",
      message: "Item C-001 in 'N801DL' is overdue by 2 days.",
      time: "1 hour ago",
    },
    {
      id: 3,
      type: "success",
      icon: CheckCircle2,
      iconColor: "text-green-600",
      bgColor: "bg-green-50",
      title: "Inspection Completed",
      message: "Project 'Airbus A320 Line Maintenance' is now completed.",
      time: "3 hours ago",
    },
    {
      id: 4,
      type: "error",
      icon: XCircle,
      iconColor: "text-red-600",
      bgColor: "bg-red-50",
      title: "Data Sync Failed",
      message: "Failed to sync inspection data for 'N802DL'. Please check connection.",
      time: "Yesterday",
    },
    {
      id: 5,
      type: "info",
      icon: Info,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-50",
      title: "New Comment",
      message: "Mike Rodriguez added a comment to 'Minor corrosion' item.",
      time: "2 days ago",
    },
    {
      id: 6,
      type: "alert",
      icon: AlertTriangle,
      iconColor: "text-orange-600",
      bgColor: "bg-orange-50",
      title: "Upcoming Maintenance",
      message: "HVAC System - Building A requires maintenance by May 10.",
      time: "3 days ago",
    },
    {
      id: 7,
      type: "success",
      icon: CheckCircle2,
      iconColor: "text-green-600",
      bgColor: "bg-green-50",
      title: "Form Submitted",
      message: "Structural Inspection Checklist for N801DL submitted.",
      time: "4 days ago",
    },
  ]

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[400px] lg:w-[450px] flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5" />
            Notifications
          </SheetTitle>
        </SheetHeader>
        <ScrollArea className="flex-1 py-4 pr-4 -mr-4">
          {" "}
          {/* Added pr-4 and -mr-4 for scrollbar */}
          <div className="space-y-4">
            {notifications.length > 0 ? (
              notifications.map((notification) => (
                <div key={notification.id} className={`flex items-start gap-3 p-3 rounded-lg ${notification.bgColor}`}>
                  <div className={`p-2 rounded-full ${notification.bgColor}`}>
                    <notification.icon className={`w-5 h-5 ${notification.iconColor}`} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{notification.title}</h4>
                    <p className="text-sm text-gray-700">{notification.message}</p>
                    <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                  </div>
                  <Button variant="ghost" size="icon" className="w-6 h-6">
                    <XCircle className="w-4 h-4 text-gray-400" />
                    <span className="sr-only">Dismiss</span>
                  </Button>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-8">No new notifications.</div>
            )}
          </div>
        </ScrollArea>
        <div className="mt-4 border-t pt-4">
          <Button variant="outline" className="w-full bg-transparent">
            Mark all as read
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  )
}
