"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Search, Plus, Users, MapPin, Phone, Mail, Award, Plane, ClipboardCheck, MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function TeamPage() {
  const [viewMode, setViewMode] = useState<"cards" | "list">("cards")
  const [filterRole, setFilterRole] = useState("all")

  const teams = [
    {
      id: 1,
      name: "Miami Inspection Hub",
      location: "Miami International Airport (MIA)",
      lead: "<PERSON>",
      members: 12,
      activeInspections: 8,
      completionRate: 94,
      specialization: "Heavy Maintenance",
      certifications: ["FAA Part 145", "EASA Part 145"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
    {
      id: 2,
      name: "JFK Quality Assurance",
      location: "John F. Kennedy Airport (JFK)",
      lead: "Mike Chen",
      members: 8,
      activeInspections: 5,
      completionRate: 98,
      specialization: "Line Maintenance",
      certifications: ["FAA Part 145", "ISO 9001"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
    {
      id: 3,
      name: "LAX Compliance Team",
      location: "Los Angeles International (LAX)",
      lead: "Emma Davis",
      members: 15,
      activeInspections: 12,
      completionRate: 91,
      specialization: "A-Check & C-Check",
      certifications: ["FAA Part 145", "EASA Part 145", "CAAC"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
    {
      id: 4,
      name: "DFW Avionics Specialists",
      location: "Dallas/Fort Worth Airport (DFW)",
      lead: "Alex Rodriguez",
      members: 10,
      activeInspections: 6,
      completionRate: 96,
      specialization: "Avionics & Electronics",
      certifications: ["FAA Part 145", "RTCA DO-178C"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
  ]

  const inspectors = [
    {
      id: 1,
      name: "Sarah Johnson",
      role: "Lead Inspector",
      team: "Miami Inspection Hub",
      location: "MIA",
      certifications: ["A&P License", "IA Certificate", "FAA Part 145"],
      experience: "15 years",
      specialization: "Boeing 737, Airbus A320",
      activeInspections: 3,
      completedThisMonth: 8,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      name: "Mike Chen",
      role: "Quality Assurance Manager",
      team: "JFK Quality Assurance",
      location: "JFK",
      certifications: ["A&P License", "Quality Manager", "EASA Part 145"],
      experience: "12 years",
      specialization: "Boeing 777, Airbus A330",
      activeInspections: 2,
      completedThisMonth: 12,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      name: "Emma Davis",
      role: "Senior Inspector",
      team: "LAX Compliance Team",
      location: "LAX",
      certifications: ["A&P License", "IA Certificate", "NDT Level II"],
      experience: "18 years",
      specialization: "Wide-body Aircraft",
      activeInspections: 4,
      completedThisMonth: 6,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 4,
      name: "Alex Rodriguez",
      role: "Avionics Specialist",
      team: "DFW Avionics Specialists",
      location: "DFW",
      certifications: ["A&P License", "Avionics Specialist", "RTCA Certified"],
      experience: "10 years",
      specialization: "Avionics Systems",
      activeInspections: 2,
      completedThisMonth: 9,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 5,
      name: "Lisa Wang",
      role: "Compliance Officer",
      team: "LAX Compliance Team",
      location: "LAX",
      certifications: ["Compliance Officer", "FAA Part 145", "ISO 9001"],
      experience: "8 years",
      specialization: "Regulatory Compliance",
      activeInspections: 1,
      completedThisMonth: 15,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 6,
      name: "David Kim",
      role: "Inspector",
      team: "Miami Inspection Hub",
      location: "MIA",
      certifications: ["A&P License", "IA Certificate"],
      experience: "6 years",
      specialization: "Regional Aircraft",
      activeInspections: 3,
      completedThisMonth: 7,
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const getCompletionColor = (rate: number) => {
    if (rate >= 95) return "text-green-600"
    if (rate >= 90) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Inspection Teams</h1>
          <p className="text-gray-600 mt-2">Manage inspection teams and personnel across all locations</p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Add Team Member
        </Button>
      </div>

      {/* Team Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Teams</p>
                <p className="text-2xl font-bold">{teams.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <ClipboardCheck className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Inspectors</p>
                <p className="text-2xl font-bold">{inspectors.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Plane className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Inspections</p>
                <p className="text-2xl font-bold">{teams.reduce((sum, team) => sum + team.activeInspections, 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Completion Rate</p>
                <p className="text-2xl font-bold">
                  {Math.round(teams.reduce((sum, team) => sum + team.completionRate, 0) / teams.length)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input placeholder="Search teams or inspectors..." className="pl-10" />
            </div>

            <Select value={filterRole} onValueChange={setFilterRole}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="lead">Lead Inspector</SelectItem>
                <SelectItem value="inspector">Inspector</SelectItem>
                <SelectItem value="qa">Quality Assurance</SelectItem>
                <SelectItem value="compliance">Compliance</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button
                variant={viewMode === "cards" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("cards")}
              >
                Teams
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                Inspectors
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Teams View */}
      {viewMode === "cards" && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {teams.map((team) => (
            <Card key={team.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{team.name}</CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{team.location}</span>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      <DropdownMenuItem>Edit Team</DropdownMenuItem>
                      <DropdownMenuItem>Manage Members</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Team Lead</p>
                    <p className="font-medium">{team.lead}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Members</p>
                    <p className="font-medium">{team.members}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600 mb-1">Specialization</p>
                  <Badge variant="outline" className="text-xs">
                    {team.specialization}
                  </Badge>
                </div>

                <div>
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Completion Rate</span>
                    <span className={`font-medium ${getCompletionColor(team.completionRate)}`}>
                      {team.completionRate}%
                    </span>
                  </div>
                  <Progress value={team.completionRate} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Active Inspections</p>
                    <p className="font-medium">{team.activeInspections}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Certifications</p>
                    <p className="font-medium">{team.certifications.length}</p>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Phone className="w-3 h-3" />
                      {team.contact.phone}
                    </div>
                    <div className="flex items-center gap-1">
                      <Mail className="w-3 h-3" />
                      {team.contact.email}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Inspectors List View */}
      {viewMode === "list" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {inspectors.map((inspector) => (
            <Card key={inspector.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={inspector.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {inspector.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{inspector.name}</h3>
                    <p className="text-sm text-gray-600">{inspector.role}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <MapPin className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{inspector.location}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 space-y-3">
                  <div>
                    <p className="text-xs text-gray-600">Team</p>
                    <p className="text-sm font-medium">{inspector.team}</p>
                  </div>

                  <div>
                    <p className="text-xs text-gray-600">Experience</p>
                    <p className="text-sm font-medium">{inspector.experience}</p>
                  </div>

                  <div>
                    <p className="text-xs text-gray-600">Specialization</p>
                    <p className="text-sm font-medium">{inspector.specialization}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 pt-3 border-t">
                    <div className="text-center">
                      <p className="text-lg font-bold text-blue-600">{inspector.activeInspections}</p>
                      <p className="text-xs text-gray-600">Active</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold text-green-600">{inspector.completedThisMonth}</p>
                      <p className="text-xs text-gray-600">Completed</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-xs text-gray-600 mb-2">Certifications</p>
                    <div className="flex flex-wrap gap-1">
                      {inspector.certifications.slice(0, 2).map((cert, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {cert}
                        </Badge>
                      ))}
                      {inspector.certifications.length > 2 && (
                        <Badge variant="secondary" className="text-xs">
                          +{inspector.certifications.length - 2}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
