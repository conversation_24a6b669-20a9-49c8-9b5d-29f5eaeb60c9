"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Save, Trash2, Upload } from "lucide-react"
import Image from "next/image"

interface BlueprintFormSheetProps {
  isOpen: boolean
  onClose: () => void
  blueprintData?: any // Optional, for editing existing blueprints
  onSave: (data: any) => void
  onDelete?: (id: number) => void
}

export function BlueprintFormSheet({ isOpen, onClose, blueprintData, onSave, onDelete }: BlueprintFormSheetProps) {
  const isEditMode = !!blueprintData
  const [formData, setFormData] = React.useState<any>(
    blueprintData || {
      name: "",
      type: "Aircraft",
      imageUrl: "",
      description: "",
    },
  )

  React.useEffect(() => {
    setFormData(
      blueprintData || {
        name: "",
        type: "Aircraft",
        imageUrl: "",
        description: "",
      },
    )
  }, [blueprintData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev: any) => ({ ...prev, [id]: value }))
  }

  const handleSelectChange = (id: string, value: string) => {
    setFormData((prev: any) => ({ ...prev, [id]: value }))
  }

  const handleSubmit = () => {
    onSave(formData)
    onClose()
  }

  const handleDelete = () => {
    if (blueprintData?.id && onDelete) {
      onDelete(blueprintData.id)
      onClose()
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full md:w-[500px] lg:w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{isEditMode ? "Edit Blueprint" : "Upload New Blueprint"}</SheetTitle>
        </SheetHeader>
        <div className="mt-6 space-y-6 pb-20">
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Blueprint Name</Label>
              <Input id="name" value={formData.name} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="type">Asset Type</Label>
              <Select value={formData.type} onValueChange={(val) => handleSelectChange("type", val)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select asset type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Aircraft">Aircraft</SelectItem>
                  <SelectItem value="Engine">Engine</SelectItem>
                  <SelectItem value="APU">APU</SelectItem>
                  <SelectItem value="Landing Gear">Landing Gear</SelectItem>
                  <SelectItem value="Component">Component</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Input id="description" value={formData.description} onChange={handleChange} />
            </div>
            <div>
              <Label htmlFor="imageUrl">Image URL (or Upload)</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="imageUrl"
                  value={formData.imageUrl}
                  placeholder="e.g., /placeholder.svg?height=100&width=150"
                  onChange={handleChange}
                />
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" /> Upload
                </Button>
              </div>
              {formData.imageUrl && (
                <div className="mt-2">
                  <Image
                    src={formData.imageUrl || "/placeholder.svg"}
                    alt="Blueprint Preview"
                    width={150}
                    height={100}
                    className="rounded-md object-cover border"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
        <SheetFooter className="flex flex-col sm:flex-row gap-2 mt-4">
          {isEditMode && (
            <Button variant="destructive" onClick={handleDelete} className="w-full sm:w-auto">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Blueprint
            </Button>
          )}
          <Button onClick={handleSubmit} className="w-full sm:w-auto">
            <Save className="w-4 h-4 mr-2" />
            {isEditMode ? "Save Changes" : "Add Blueprint"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
