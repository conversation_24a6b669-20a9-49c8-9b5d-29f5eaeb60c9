"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, Users, Target, FileText, Folder, Edit, Share, Cog, Settings, AlertTriangle, CheckCircle, Clock } from 'lucide-react'

export default function EngineProjectDetail() {
  const [activeTab, setActiveTab] = useState("overview")

  // Example Engine-only project
  const project = {
    id: "engine-001",
    name: "CFM56-7B Engine Shop Visit Program",
    status: "In Progress", 
    category: "Engine Shop Visit",
    progress: 45,
    description:
      "Comprehensive engine shop visit program for CFM56-7B engines. Includes hot section inspections, performance restoration, LLP replacements, and compliance with all applicable Service Bulletins and Airworthiness Directives.",
    manager: "James Wilson",
    startDate: "March 1, 2024",
    endDate: "June 30, 2024", 
    location: "Engine Overhaul Facility - Dallas",
    priority: "High",
    inspectionType: "Shop Visit",
    lessee: "Southwest Airlines",
    assetType: "Engine", // Engine-only project
    team: [
      { name: "James Wilson", role: "Engine Shop Manager", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Maria Garcia", role: "Hot Section Inspector", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Robert Chen", role: "LLP Specialist", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Jennifer Adams", role: "Quality Inspector", avatar: "/placeholder.svg?height=40&width=40" },
    ],
  }

  // Engine assets (main project assets)
  const engines = [
    {
      id: 1,
      model: "CFM56-7B24",
      msn: "872456",
      status: "In Shop Visit",
      progress: 60,
      location: "Engine Shop Bay 3",
      nextDue: "Performance Restoration",
      tsn: "22,100",
      csn: "15,800",
      shopVisitType: "Hot Section Inspection",
      aircraftRegistration: "N789SW", // Reference to parent aircraft
    },
    {
      id: 2,
      model: "CFM56-7B24", 
      msn: "872457",
      status: "Pending",
      progress: 0,
      location: "Induction Queue",
      nextDue: "Complete Overhaul",
      tsn: "28,450",
      csn: "18,200", 
      shopVisitType: "Complete Overhaul",
      aircraftRegistration: "N790SW",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Shop Visit":
      case "In Progress":
        return "bg-blue-100 text-blue-800"
      case "Pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case "In Shop Visit":
      case "In Progress":
        return <Settings className="w-4 h-4 text-blue-600" />
      case "Pending":
        return <Clock className="w-4 h-4 text-yellow-600" />
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Project Header */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
          <div className="flex items-center gap-4 mt-2">
            <Badge className="bg-blue-100 text-blue-800">{project.status}</Badge>
            <Badge variant="outline">{project.category}</Badge>
            <Badge variant="outline" className="bg-purple-50 text-purple-700">
              <Cog className="w-3 h-3 mr-1" />
              {project.lessee}
            </Badge>
            <span className="text-gray-600">Managed by {project.manager}</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit Project
          </Button>
        </div>
      </div>

      {/* Project Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Progress</p>
                <p className="text-2xl font-bold">{project.progress}%</p>
              </div>
            </div>
            <Progress value={project.progress} className="mt-4" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Calendar className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Timeline</p>
                <p className="text-sm font-semibold">{project.startDate}</p>
                <p className="text-sm font-semibold">{project.endDate}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Team Size</p>
                <p className="text-2xl font-bold">{project.team.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Cog className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Assets</p>
                <p className="text-2xl font-bold">{engines.length}</p>
                <p className="text-xs text-gray-500">{project.assetType}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabbed Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full max-w-2xl grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="assets">Assets</TabsTrigger>
          <TabsTrigger value="forms">Inspection Forms</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Project Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Description</h4>
                  <p className="text-gray-600 mb-6">{project.description}</p>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Start Date:</span>
                      <span className="font-medium">{project.startDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">End Date:</span>
                      <span className="font-medium">{project.endDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span className="font-medium">{project.location}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Asset Type:</span>
                      <Badge className="bg-orange-100 text-orange-800">{project.assetType}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Priority:</span>
                      <Badge className="bg-red-100 text-red-800">{project.priority}</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Engine Shop Team</h4>
                  <div className="space-y-3">
                    {project.team.map((member, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <Avatar>
                          <AvatarImage src={member.avatar || "/placeholder.svg"} />
                          <AvatarFallback>
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h5 className="font-medium text-gray-900">{member.name}</h5>
                          <p className="text-sm text-gray-600">{member.role}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Assets Tab */}
        <TabsContent value="assets" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cog className="w-5 h-5" />
                {project.assetType} Assets ({engines.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {engines.map((engine) => (
                  <div key={engine.id} className="border rounded-lg p-6 bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Cog className="w-5 h-5 text-orange-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{engine.model}</h3>
                          <p className="text-sm text-gray-600">
                            <span className="font-mono font-semibold">MSN: {engine.msn}</span>
                          </p>
                          <p className="text-xs text-gray-500">
                            From Aircraft: {engine.aircraftRegistration}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(engine.status)}
                        <Badge className={getStatusColor(engine.status)}>{engine.status}</Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Engine Details</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Location:</span>
                            <span>{engine.location}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">TSN:</span>
                            <span>{engine.tsn} hours</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">CSN:</span>
                            <span>{engine.csn} cycles</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Shop Visit Type:</span>
                            <span>{engine.shopVisitType}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Work Scope</h4>
                        <div className="space-y-2">
                          <div className="p-2 bg-white rounded border">
                            <p className="text-sm font-medium">Hot Section Inspection</p>
                            <p className="text-xs text-gray-600">Borescope & Visual Inspection</p>
                          </div>
                          <div className="p-2 bg-white rounded border">
                            <p className="text-sm font-medium">LLP Replacement</p>
                            <p className="text-xs text-gray-600">Turbine Disk & Blades</p>
                          </div>
                          <div className="p-2 bg-white rounded border">
                            <p className="text-sm font-medium">Performance Test</p>
                            <p className="text-xs text-gray-600">Post-Maintenance Run</p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Compliance</h4>
                        <div className="space-y-2">
                          <div className="p-2 bg-white rounded border">
                            <p className="text-sm font-medium">Service Bulletins</p>
                            <p className="text-xs text-gray-600">All Current SBs Applied</p>
                          </div>
                          <div className="p-2 bg-white rounded border">
                            <p className="text-sm font-medium">Airworthiness Directives</p>
                            <p className="text-xs text-gray-600">AD Compliance Verified</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">Shop Visit Progress</span>
                        <span className="text-sm font-medium">{engine.progress}%</span>
                      </div>
                      <Progress value={engine.progress} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inspection Forms Tab */}
        <TabsContent value="forms" className="space-y-6">
          <EngineShopVisitForm />
          <EngineLLPForm />
          <EngineComplianceForm />
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="w-5 h-5" />
                Documentation & Records
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { name: "Engine Shop Visit Work Package", type: "PDF", size: "3.2 MB", date: "Mar 1, 2024" },
                  { name: "Hot Section Inspection Reports", type: "PDF", size: "5.1 MB", date: "Mar 15, 2024" },
                  { name: "LLP Replacement Documentation", type: "PDF", size: "2.8 MB", date: "Mar 20, 2024" },
                  { name: "Engine Test Cell Results", type: "XLS", size: "1.4 MB", date: "Mar 25, 2024" },
                ].map((doc, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                        <FileText className="w-4 h-4 text-gray-600" />
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">{doc.name}</h5>
                        <p className="text-sm text-gray-600">
                          {doc.type} • {doc.size} • {doc.date}
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Engine-specific forms
function EngineShopVisitForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Cog className="w-5 h-5" />
          Engine Shop Visit Form
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div>
            <Label htmlFor="engine-msn">Engine MSN</Label>
            <Input id="engine-msn" placeholder="e.g., 872456" />
          </div>
          <div>
            <Label htmlFor="engine-tsn">Engine TSN (Hours)</Label>
            <Input id="engine-tsn" placeholder="e.g., 22,100" />
          </div>
          <div>
            <Label htmlFor="engine-csn">Engine CSN (Cycles)</Label>
            <Input id="engine-csn" placeholder="e.g., 15,800" />
          </div>
        </div>

        <div>
          <Label htmlFor="shop-visit-type">Shop Visit Type</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select shop visit type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hsi">Hot Section Inspection</SelectItem>
              <SelectItem value="performance-restoration">Performance Restoration</SelectItem>
              <SelectItem value="overhaul">Complete Overhaul</SelectItem>
              <SelectItem value="repair">Repair Visit</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Engine Components Inspected</Label>
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center space-x-2">
              <Checkbox id="compressor" />
              <Label htmlFor="compressor">Compressor</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="combustor" />
              <Label htmlFor="combustor">Combustor</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="turbine" />
              <Label htmlFor="turbine">Turbine</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="accessories" />
              <Label htmlFor="accessories">Accessories</Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function EngineLLPForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Engine Life Limited Parts (LLP) Form</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="llp-replacement">LLP Replacement Required</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select LLP status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No LLP Replacement</SelectItem>
              <SelectItem value="disk">Disk Replacement</SelectItem>
              <SelectItem value="blade">Blade Replacement</SelectItem>
              <SelectItem value="multiple">Multiple LLP Replacement</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="llp-documentation">LLP Documentation Status</Label>
          <Textarea id="llp-documentation" placeholder="Document LLP tracking and replacement details..." />
        </div>
      </CardContent>
    </Card>
  )
}

function EngineComplianceForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Engine Compliance Form</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="sb-compliance">Service Bulletin Compliance</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select compliance status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current">All SBs Current</SelectItem>
              <SelectItem value="pending">SBs Pending</SelectItem>
              <SelectItem value="not-applicable">Not Applicable</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="ad-compliance">Airworthiness Directive Compliance</Label>
          <Textarea id="ad-compliance" placeholder="Document AD compliance status..." />
        </div>
      </CardContent>
    </Card>
  )
}
