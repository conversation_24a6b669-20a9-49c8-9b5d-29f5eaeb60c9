"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Edit, Trash2, MoreHorizontal, MapPin } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TeamFormSheet } from "@/components/team-form-sheet" // Import the new sheet component

export default function TeamABM() {
  const [teams, setTeams] = useState([
    {
      id: 1,
      name: "Miami Inspection Hub",
      location: "Miami International Airport (MIA)",
      lead: "<PERSON>",
      members: 12,
      activeInspections: 8,
      completionRate: 94,
      specialization: "Heavy Maintenance",
      certifications: ["FAA Part 145", "EASA Part 145"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
    {
      id: 2,
      name: "JFK Quality Assurance",
      location: "John F. Kennedy Airport (JFK)",
      lead: "Mike Chen",
      members: 8,
      activeInspections: 5,
      completionRate: 98,
      specialization: "Line Maintenance",
      certifications: ["FAA Part 145", "ISO 9001"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
    {
      id: 3,
      name: "LAX Compliance Team",
      location: "Los Angeles International (LAX)",
      lead: "Emma Davis",
      members: 15,
      activeInspections: 12,
      completionRate: 91,
      specialization: "A-Check & C-Check",
      certifications: ["FAA Part 145", "EASA Part 145", "CAAC"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
    {
      id: 4,
      name: "DFW Avionics Specialists",
      location: "Dallas/Fort Worth Airport (DFW)",
      lead: "Alex Rodriguez",
      members: 10,
      activeInspections: 6,
      completionRate: 96,
      specialization: "Avionics & Electronics",
      certifications: ["FAA Part 145", "RTCA DO-178C"],
      contact: {
        phone: "+****************",
        email: "<EMAIL>",
      },
    },
  ])

  const [isTeamSheetOpen, setIsTeamSheetOpen] = useState(false)
  const [teamToEdit, setTeamToEdit] = useState<any | null>(null)

  const handleNewTeamClick = () => {
    setTeamToEdit(null)
    setIsTeamSheetOpen(true)
  }

  const handleEditTeamClick = (team: any) => {
    setTeamToEdit(team)
    setIsTeamSheetOpen(true)
  }

  const handleSaveTeam = (data: any) => {
    if (data.id) {
      // Edit existing team
      setTeams((prev) => prev.map((team) => (team.id === data.id ? { ...team, ...data } : team)))
      console.log("Team updated:", data)
    } else {
      // Create new team
      const newId = Math.max(...teams.map((team) => team.id)) + 1
      const newTeam = { ...data, id: newId }
      setTeams((prev) => [...prev, newTeam])
      console.log("New team created:", newTeam)
    }
  }

  const handleDeleteTeam = (id: number) => {
    setTeams(teams.filter((team) => team.id !== id))
    console.log("Team deleted:", id)
  }

  const getCompletionColor = (rate: number) => {
    if (rate >= 95) return "text-green-600"
    if (rate >= 90) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle>Team Management</CardTitle>
        <Button onClick={handleNewTeamClick}>
          <Plus className="w-4 h-4 mr-2" /> Add New Team
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Registered Teams</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Lead</TableHead>
                <TableHead>Members</TableHead>
                <TableHead>Specialization</TableHead>
                <TableHead>Completion Rate</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teams.map((team) => (
                <TableRow key={team.id}>
                  <TableCell>{team.id}</TableCell>
                  <TableCell className="font-medium">{team.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3 text-gray-500" />
                      {team.location}
                    </div>
                  </TableCell>
                  <TableCell>{team.lead}</TableCell>
                  <TableCell>{team.members}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className="text-xs">
                      {team.specialization}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={team.completionRate} className="w-16 h-2" />
                      <span className={`text-sm font-medium ${getCompletionColor(team.completionRate)}`}>
                        {team.completionRate}%
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditTeamClick(team)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteTeam(team.id)}>
                          <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <TeamFormSheet
        isOpen={isTeamSheetOpen}
        onClose={() => setIsTeamSheetOpen(false)}
        teamData={teamToEdit}
        onSave={handleSaveTeam}
        onDelete={handleDeleteTeam}
      />
    </Card>
  )
}
