"use client"

import { <PERSON>, Bell } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useState } from "react"
import { NotificationSheet } from "./notification-sheet" // Import the new notification sheet

export function Header() {
  const [isNotificationsSheetOpen, setIsNotificationsSheetOpen] = useState(false)

  return (
    <header className="bg-white border-b border-gray-200 px-4 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Overview</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Search projects..." className="pl-10 w-64" />
          </div>

          <Button variant="ghost" size="icon" onClick={() => setIsNotificationsSheetOpen(true)}>
            <Bell className="w-5 h-5" />
          </Button>
        </div>
      </div>
      <NotificationSheet isOpen={isNotificationsSheetOpen} onClose={() => setIsNotificationsSheetOpen(false)} />
    </header>
  )
}
